/* BreezVillas Property & Amenities - Elegant Luxury Theme */
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700;800&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Open Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #dee2e6 100%);
    color: #495057;
    line-height: 1.6;
    overflow-x: hidden;
}

/* Hero Section */
.breezwp-hero-section {
    min-height: 100vh;
    background: linear-gradient(135deg, rgba(248, 249, 250, 0.95) 0%, rgba(233, 236, 239, 0.9) 100%),
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%239029a7" stop-opacity="0.1"/><stop offset="100%" stop-color="%23602d92" stop-opacity="0.05"/></radialGradient></defs><rect width="100%" height="100%" fill="url(%23a)"/></svg>');
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.breezwp-hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23000000" opacity="0.02"/><circle cx="75" cy="75" r="1" fill="%23000000" opacity="0.02"/><circle cx="50" cy="10" r="0.5" fill="%23000000" opacity="0.01"/><circle cx="10" cy="60" r="0.5" fill="%23000000" opacity="0.01"/><circle cx="90" cy="40" r="0.5" fill="%23000000" opacity="0.01"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.breezwp-hero-content {
    max-width: 1200px;
    padding: 0 2rem;
    z-index: 2;
    position: relative;
}

.breezwp-hero-title {
    font-family: 'Open Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: clamp(3rem, 8vw, 6rem);
    font-weight: 800;
    background: linear-gradient(135deg, #9029a7 0%, #602d92 50%, #3bc3d4 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1.5rem;
    letter-spacing: -0.02em;
    animation: breezwp-fadeInUp 1s ease-out;
}

.breezwp-hero-subtitle {
    font-size: clamp(1.2rem, 3vw, 1.5rem);
    color: #6c757d;
    margin-bottom: 3rem;
    font-weight: 400;
    animation: breezwp-fadeInUp 1s ease-out 0.2s both;
}

.breezwp-hero-stats {
    display: flex;
    justify-content: center;
    gap: 4rem;
    flex-wrap: wrap;
    animation: breezwp-fadeInUp 1s ease-out 0.4s both;
    margin-bottom: 2rem;
}

.breezwp-stat-item {
    text-align: center;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(222, 226, 230, 0.3);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.breezwp-stat-item:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.95);
    border-color: rgba(144, 41, 167, 0.3);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.breezwp-stat-number {
    display: block;
    font-family: 'Open Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 2.5rem;
    font-weight: 700;
    color: #9029a7;
    margin-bottom: 0.5rem;
}

.breezwp-stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    font-weight: 500;
}

.breezwp-hero-cta {
    animation: breezwp-fadeInUp 1s ease-out 0.6s both;
}

.breezwp-cta-badge {
    display: inline-block;
    background: linear-gradient(135deg, #9029a7, #602d92);
    color: #ffffff;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1.1rem;
    box-shadow: 0 10px 30px rgba(144, 41, 167, 0.3);
    transition: all 0.3s ease;
    text-decoration: none;
    cursor: pointer;
}

.breezwp-cta-badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 40px rgba(144, 41, 167, 0.4);
}

/* Main Content */
.breezwp-main-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 4rem 2rem;
}

/* Sections */
.breezwp-section {
    margin-bottom: 6rem;
    animation: breezwp-fadeInUp 0.8s ease-out;
}

.breezwp-section-featured {
    background: linear-gradient(135deg, rgba(144, 41, 167, 0.05) 0%, rgba(96, 45, 146, 0.05) 100%);
    padding: 4rem 2rem;
    border-radius: 20px;
    margin: 4rem 0;
}

/* Gallery Navigation */
.breezwp-gallery-nav {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 3rem;
}

.breezwp-gallery-filter {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(222, 226, 230, 0.3);
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    color: #495057;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.breezwp-gallery-filter:hover {
    background: rgba(255, 255, 255, 1);
    border-color: rgba(144, 41, 167, 0.3);
    transform: translateY(-2px);
}

.breezwp-gallery-filter.active {
    background: linear-gradient(135deg, #9029a7, #602d92);
    color: #ffffff;
    border-color: transparent;
}

/* Responsive Gallery Navigation */
@media (max-width: 768px) {
    .breezwp-gallery-nav {
        flex-direction: column;
        align-items: center;
    }
    
    .breezwp-gallery-filter {
        width: 100%;
        max-width: 200px;
        text-align: center;
    }
}

/* Call to Action Section */
.breezwp-cta-section {
    background: linear-gradient(135deg, rgba(144, 41, 167, 0.1) 0%, rgba(96, 45, 146, 0.1) 100%);
    padding: 4rem 2rem;
    text-align: center;
    border-radius: 20px;
    margin: 4rem 0;
}

.breezwp-cta-content h2 {
    font-size: clamp(2rem, 5vw, 3rem);
    font-weight: 700;
    color: #9029a7;
    margin-bottom: 1rem;
}

.breezwp-cta-content p {
    font-size: 1.2rem;
    color: #6c757d;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.breezwp-cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.breezwp-book-btn {
    background: linear-gradient(135deg, #9029a7, #602d92);
    color: #ffffff;
    padding: 1.2rem 2.5rem;
    border-radius: 30px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.4s ease;
    box-shadow: 0 8px 25px rgba(144, 41, 167, 0.3);
    position: relative;
    overflow: hidden;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.breezwp-book-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.breezwp-book-btn:hover::before {
    left: 100%;
}

.breezwp-book-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(144, 41, 167, 0.4);
    background: linear-gradient(135deg, #a532b8, #6d3299);
}

.breezwp-secondary-btn {
    background: rgba(255, 255, 255, 0.9);
    color: #495057;
    padding: 1.2rem 2.5rem;
    border-radius: 30px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.4s ease;
    border: 2px solid rgba(144, 41, 167, 0.2);
    backdrop-filter: blur(15px);
    position: relative;
    overflow: hidden;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.breezwp-secondary-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(144, 41, 167, 0.1), transparent);
    transition: left 0.5s ease;
}

.breezwp-secondary-btn:hover::before {
    left: 100%;
}

.breezwp-secondary-btn:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(144, 41, 167, 0.2);
    border-color: rgba(144, 41, 167, 0.4);
    color: #9029a7;
}

/* Responsive CTA */
@media (max-width: 768px) {
    .breezwp-cta-section {
        padding: 3rem 1rem;
    }
    
    .breezwp-cta-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .breezwp-book-btn,
    .breezwp-secondary-btn {
        width: 100%;
        max-width: 250px;
        text-align: center;
    }
}

/* Footer */
.breezwp-footer {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: #ffffff;
    padding: 4rem 2rem 2rem;
    margin-top: 6rem;
}

.breezwp-footer-content {
    max-width: 1400px;
    margin: 0 auto;
}

.breezwp-footer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
    margin-bottom: 3rem;
}

.breezwp-footer-section h3,
.breezwp-footer-section h4 {
    color: #ffffff;
    margin-bottom: 1rem;
    font-weight: 600;
}

.breezwp-footer-section p {
    color: #bdc3c7;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.breezwp-footer-section ul {
    list-style: none;
}

.breezwp-footer-section ul li {
    margin-bottom: 0.5rem;
}

.breezwp-footer-section ul li a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.breezwp-footer-section ul li a:hover {
    color: #9029a7;
}

/* Footer Contact Links */
.breezwp-footer-section p a {
    color: #ffffff;
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: 500;
}

.breezwp-footer-section p a:hover {
    color: #3bc3d4;
    text-decoration: underline;
}

/* Contact Info Styling */
.breezwp-contact-info-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.8rem;
}

.breezwp-contact-info-item i {
    color: #3bc3d4;
    width: 20px;
    text-align: center;
}

.breezwp-contact-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-top: 0.3rem;
}

.breezwp-whatsapp-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, #25d366, #128c7e);
    color: #ffffff !important;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    text-decoration: none !important;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 3px 10px rgba(37, 211, 102, 0.3);
}

.breezwp-whatsapp-link:hover {
    background: linear-gradient(135deg, #128c7e, #25d366);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(37, 211, 102, 0.4);
    color: #ffffff !important;
}

.breezwp-whatsapp-link i {
    font-size: 1.1rem;
}

.breezwp-social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.breezwp-social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: #ffffff;
    text-decoration: none;
    transition: all 0.3s ease;
}

.breezwp-social-links a:hover {
    background: #9029a7;
    transform: translateY(-2px);
}

.breezwp-footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.breezwp-footer-bottom p {
    color: #bdc3c7;
    margin: 0;
}

.breezwp-footer-links {
    display: flex;
    gap: 2rem;
}

.breezwp-footer-links a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.breezwp-footer-links a:hover {
    color: #9029a7;
}

/* Responsive Footer */
@media (max-width: 768px) {
    .breezwp-footer {
        padding: 3rem 1rem 2rem;
    }
    
    .breezwp-footer-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .breezwp-footer-bottom {
        flex-direction: column;
        text-align: center;
    }
    
    .breezwp-footer-links {
        justify-content: center;
    }

    .breezwp-contact-actions {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-start;
    }

    .breezwp-whatsapp-link {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }

    .breezwp-contact-info-item {
        justify-content: center;
        text-align: center;
    }
}

.breezwp-section-featured::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #9029a7, #602d92, #3bc3d4);
}

.breezwp-section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.breezwp-icon-wrapper {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #9029a7 0%, #602d92 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    box-shadow: 0 10px 30px rgba(144, 41, 167, 0.3);
    transition: all 0.3s ease;
}

.breezwp-icon-wrapper:hover {
    transform: scale(1.1);
    box-shadow: 0 15px 40px rgba(144, 41, 167, 0.4);
}

.breezwp-icon-wrapper i {
    font-size: 2rem;
    color: #ffffff;
}

.breezwp-section-header h2 {
    font-family: 'Open Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 2.8rem;
    font-weight: 700;
    color: #343a40;
    margin-bottom: 1.5rem;
    position: relative;
    letter-spacing: -0.01em;
}

.breezwp-section-header h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #9029a7, #3bc3d4);
    border-radius: 2px;
}

.breezwp-section-header p {
    font-size: 1.2rem;
    color: #6c757d;
    max-width: 650px;
    margin: 0 auto;
    line-height: 1.7;
    font-weight: 400;
}

/* Overview Grid */
.breezwp-overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.breezwp-overview-card {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    padding: 2rem;
    border: 1px solid rgba(222, 226, 230, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
}

.breezwp-overview-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 1);
    border-color: rgba(144, 41, 167, 0.3);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.breezwp-overview-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 15px;
    margin-bottom: 1.5rem;
}

.breezwp-overview-card h3 {
    font-family: 'Open Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 1.3rem;
    font-weight: 600;
    color: #343a40;
    margin-bottom: 0.8rem;
}

.breezwp-overview-card p {
    color: #6c757d;
    line-height: 1.6;
}

/* Content Grid */
.breezwp-content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.breezwp-content-reverse {
    direction: rtl;
}

.breezwp-content-reverse > * {
    direction: ltr;
}

.breezwp-content-image {
    text-align: center;
}

.breezwp-property-image {
    width: 100%;
    max-width: 500px;
    height: auto;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.breezwp-content-text {
    padding: 2rem;
}

.breezwp-amenity-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.breezwp-amenity-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 15px;
    border: 1px solid rgba(222, 226, 230, 0.3);
    transition: all 0.3s ease;
}

.breezwp-amenity-item:hover {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(144, 41, 167, 0.3);
    transform: translateX(5px);
}

.breezwp-amenity-item i {
    color: #3bc3d4;
    font-size: 1.2rem;
    margin-top: 0.2rem;
    flex-shrink: 0;
}

.breezwp-amenity-item span {
    color: #343a40;
    line-height: 1.6;
}

/* Amenity Grid */
.breezwp-amenity-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.breezwp-amenity-card {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    padding: 2.5rem;
    border: 1px solid rgba(222, 226, 230, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
}

.breezwp-amenity-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #9029a7, #602d92);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.breezwp-amenity-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 1);
    border-color: rgba(144, 41, 167, 0.3);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.breezwp-amenity-card:hover::before {
    transform: scaleX(1);
}

.breezwp-amenity-card i {
    color: #3bc3d4;
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    display: block;
}

.breezwp-amenity-card h3 {
    font-family: 'Open Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 1.3rem;
    font-weight: 600;
    color: #343a40;
    margin-bottom: 1rem;
}

.breezwp-amenity-card p {
    color: #6c757d;
    line-height: 1.6;
}

/* Included Grid */
.breezwp-included-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.breezwp-included-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 15px;
    border: 1px solid rgba(222, 226, 230, 0.3);
    transition: all 0.3s ease;
}

.breezwp-included-item:hover {
    background: rgba(255, 255, 255, 0.95);
    border-color: rgba(144, 41, 167, 0.3);
    transform: translateY(-2px);
}

.breezwp-included-item i {
    color: #3bc3d4;
    font-size: 1.5rem;
}

.breezwp-included-item span {
    color: #343a40;
    font-weight: 500;
}

/* Info Grid */
.breezwp-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.breezwp-info-item {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    padding: 2rem;
    border: 1px solid rgba(222, 226, 230, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
}

.breezwp-info-item:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 1);
    border-color: rgba(144, 41, 167, 0.3);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.breezwp-info-item i {
    color: #3bc3d4;
    font-size: 2rem;
    margin-bottom: 1rem;
    display: block;
}

.breezwp-info-item h3 {
    font-family: 'Open Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 1.2rem;
    font-weight: 600;
    color: #343a40;
    margin-bottom: 0.8rem;
}

.breezwp-info-item p {
    color: #6c757d;
    line-height: 1.6;
}

.breezwp-info-warning {
    background: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.3);
}

.breezwp-info-warning i {
    color: #ef4444;
}

/* Checkout Grid */
.breezwp-checkout-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.breezwp-checkout-item {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    padding: 2rem;
    border: 1px solid rgba(222, 226, 230, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
}

.breezwp-checkout-item:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 1);
    border-color: rgba(144, 41, 167, 0.3);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.breezwp-checkout-item i {
    color: #3bc3d4;
    font-size: 2rem;
    margin-bottom: 1rem;
    display: block;
}

.breezwp-checkout-item h3 {
    font-family: 'Open Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 1.2rem;
    font-weight: 600;
    color: #343a40;
    margin-bottom: 0.8rem;
}

.breezwp-checkout-item p {
    color: #6c757d;
    line-height: 1.6;
}

/* Rate Grid */
.breezwp-rate-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.breezwp-rate-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 25px;
    padding: 2.5rem;
    border: 2px solid transparent;
    backdrop-filter: blur(15px);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08);
}

.breezwp-rate-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #9029a7, #602d92, #3bc3d4);
    border-radius: 25px 25px 0 0;
}

.breezwp-rate-card:hover {
    transform: translateY(-8px);
    background: rgba(255, 255, 255, 1);
    border-color: rgba(144, 41, 167, 0.2);
    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.15);
}

.breezwp-rate-card h3 {
    font-family: 'Open Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 1.5rem;
    font-weight: 700;
    color: #343a40;
    margin-bottom: 1rem;
    text-align: center;
}

.breezwp-rate-period {
    font-size: 1.1rem;
    font-weight: 600;
    color: #9029a7;
    text-align: center;
    margin-bottom: 1rem;
    padding: 0.5rem 1rem;
    background: rgba(144, 41, 167, 0.1);
    border-radius: 15px;
    display: inline-block;
    width: 100%;
    box-sizing: border-box;
}

.breezwp-rate-description {
    color: #6c757d;
    line-height: 1.6;
    text-align: center;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
}

.breezwp-rate-features {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.breezwp-rate-features span {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    color: #495057;
    font-size: 0.9rem;
    font-weight: 500;
}

.breezwp-rate-features i {
    color: #3bc3d4;
    font-size: 0.9rem;
    width: 16px;
    text-align: center;
}

/* Enhanced Availability Widget */
.breezwp-availability-widget {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 25px;
    padding: 2rem;
    border: 1px solid rgba(222, 226, 230, 0.3);
    backdrop-filter: blur(15px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08);
    margin: 2rem 0;
    position: relative;
    overflow: hidden;
}

.breezwp-availability-widget::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #9029a7, #602d92, #3bc3d4);
    border-radius: 25px 25px 0 0;
}

/* Enhanced Info Items */
.breezwp-info-item {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 25px;
    padding: 2.5rem;
    border: 1px solid rgba(222, 226, 230, 0.3);
    backdrop-filter: blur(15px);
    transition: all 0.4s ease;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
}

.breezwp-info-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #3bc3d4, #9029a7);
    border-radius: 25px 25px 0 0;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.breezwp-info-item:hover::before {
    opacity: 1;
}

.breezwp-info-item:hover {
    transform: translateY(-8px);
    background: rgba(255, 255, 255, 1);
    border-color: rgba(144, 41, 167, 0.3);
    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.15);
}

/* Section CTA Buttons */
.breezwp-section-cta {
    text-align: center;
    margin-top: 4rem;
    padding-top: 3rem;
    border-top: 1px solid rgba(222, 226, 230, 0.3);
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Animations */
@keyframes breezwp-fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes breezwp-float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .breezwp-amenity-grid,
    .breezwp-checkout-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .breezwp-hero-stats {
        gap: 2rem;
    }
    
    .breezwp-stat-item {
        padding: 1.5rem;
    }
    
    .breezwp-main-content {
        padding: 2rem 1rem;
    }
    
    .breezwp-section-featured {
        padding: 2rem;
    }
    
    .breezwp-content-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .breezwp-amenity-grid,
    .breezwp-overview-grid,
    .breezwp-checkout-grid {
        grid-template-columns: 1fr;
    }
    
    .breezwp-section-header h2 {
        font-size: 2rem;
    }
    
    .breezwp-hero-title {
        font-size: 3rem;
    }
    
    .breezwp-cta-section {
        padding: 2rem;
    }
}

@media (max-width: 480px) {
    .breezwp-hero-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .breezwp-stat-item {
        padding: 1rem;
    }
    
    .breezwp-amenity-card,
    .breezwp-overview-card {
        padding: 1.5rem;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #e9ecef;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #9029a7, #602d92);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #602d92, #3bc3d4);
}

/* Selection */
::selection {
    background: rgba(144, 41, 167, 0.3);
    color: #343a40;
}

/* Focus States */
.breezwp-amenity-card:focus,
.breezwp-overview-card:focus,
.breezwp-stat-item:focus {
    outline: 2px solid #9029a7;
    outline-offset: 2px;
}

/* Hover Effects */
.breezwp-icon-wrapper:hover i {
    transform: scale(1.1);
}

.breezwp-amenity-card:hover i {
    animation: breezwp-float 2s ease-in-out infinite;
} 

/* Carousel Styles */
.breezwp-carousel-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 0;
}

.breezwp-carousel-main {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 2rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.breezwp-carousel-slide {
    position: relative;
    width: 100%;
    max-width: 800px;
    height: 500px;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.breezwp-carousel-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.breezwp-carousel-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, rgba(144, 41, 167, 0.9) 0%, rgba(96, 45, 146, 0.9) 100%);
    color: #ffffff;
    padding: 1.5rem;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.breezwp-carousel-slide:hover .breezwp-carousel-caption {
    transform: translateY(0);
}

.breezwp-carousel-caption h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.breezwp-carousel-caption p {
    font-size: 1rem;
    opacity: 0.9;
    line-height: 1.4;
}

.breezwp-carousel-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
    backdrop-filter: blur(10px);
}

.breezwp-carousel-nav:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.breezwp-carousel-nav i {
    font-size: 1.2rem;
    color: #9029a7;
}

.breezwp-carousel-prev {
    left: -25px;
}

.breezwp-carousel-next {
    right: -25px;
}

.breezwp-carousel-thumbnails {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 2rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.breezwp-carousel-thumbnail {
    width: 80px;
    height: 80px;
    border-radius: 10px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 3px solid transparent;
    opacity: 0.7;
}

.breezwp-carousel-thumbnail:hover {
    transform: scale(1.1);
    opacity: 1;
    border-color: rgba(144, 41, 167, 0.5);
}

.breezwp-carousel-thumbnail.active {
    border-color: #9029a7;
    opacity: 1;
    transform: scale(1.05);
}

.breezwp-carousel-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.breezwp-carousel-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.breezwp-carousel-play-pause {
    background: linear-gradient(135deg, #9029a7, #602d92);
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #ffffff;
    font-size: 1.2rem;
}

.breezwp-carousel-play-pause:hover {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(144, 41, 167, 0.3);
}

.breezwp-carousel-counter {
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
    background: rgba(255, 255, 255, 0.8);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

/* Responsive Carousel */
@media (max-width: 768px) {
    .breezwp-carousel-main {
        padding: 1rem;
    }
    
    .breezwp-carousel-slide {
        height: 300px;
    }
    
    .breezwp-carousel-nav {
        width: 40px;
        height: 40px;
    }
    
    .breezwp-carousel-nav i {
        font-size: 1rem;
    }
    
    .breezwp-carousel-prev {
        left: -20px;
    }
    
    .breezwp-carousel-next {
        right: -20px;
    }
    
    .breezwp-carousel-thumbnails {
        gap: 0.5rem;
    }
    
    .breezwp-carousel-thumbnail {
        width: 60px;
        height: 60px;
    }
    
    .breezwp-carousel-controls {
        flex-direction: column;
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .breezwp-carousel-slide {
        height: 250px;
    }
    
    .breezwp-carousel-nav {
        width: 35px;
        height: 35px;
    }
    
    .breezwp-carousel-prev {
        left: -17px;
    }
    
    .breezwp-carousel-next {
        right: -17px;
    }
    
    .breezwp-carousel-thumbnail {
        width: 50px;
        height: 50px;
    }
} 

.breezwp-nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 80px;
}

.breezwp-logo a {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    color: #9029a7;
    font-weight: 700;
    font-size: 1.5rem;
}

.breezwp-logo i {
    font-size: 2rem;
}

.breezwp-nav-menu {
    display: flex;
    align-items: center;
}

.breezwp-nav-list {
    display: flex;
    list-style: none;
    gap: 2rem;
    align-items: center;
}

.breezwp-nav-link {
    text-decoration: none;
    color: #495057;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.5rem 0;
}

.breezwp-nav-link:hover {
    color: #9029a7;
}

.breezwp-nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #9029a7, #602d92);
    transition: width 0.3s ease;
}

.breezwp-nav-link:hover::after {
    width: 100%;
}

.breezwp-book-now {
    background: linear-gradient(135deg, #9029a7, #602d92);
    color: #ffffff !important;
    padding: 0.75rem 1.5rem !important;
    border-radius: 25px;
    box-shadow: 0 5px 15px rgba(144, 41, 167, 0.3);
}

.breezwp-book-now:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(144, 41, 167, 0.4);
}

.breezwp-book-now::after {
    display: none;
}

/* Dropdown Menu */
.breezwp-dropdown {
    position: relative;
}

.breezwp-dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(222, 226, 230, 0.3);
    border-radius: 15px;
    padding: 1rem 0;
    min-width: 200px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.breezwp-dropdown:hover .breezwp-dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.breezwp-dropdown-menu li {
    list-style: none;
}

.breezwp-dropdown-menu a {
    display: block;
    padding: 0.75rem 1.5rem;
    color: #495057;
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: 500;
}

.breezwp-dropdown-menu a:hover {
    background: rgba(144, 41, 167, 0.1);
    color: #9029a7;
    padding-left: 2rem;
}

/* Mobile Navigation Toggle */
.breezwp-nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.breezwp-nav-toggle .bar {
    width: 25px;
    height: 3px;
    background: #9029a7;
    border-radius: 2px;
    transition: all 0.3s ease;
}

.breezwp-nav-toggle.active .bar:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.breezwp-nav-toggle.active .bar:nth-child(2) {
    opacity: 0;
}

.breezwp-nav-toggle.active .bar:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* Responsive Navigation */
@media (max-width: 1024px) {
    .breezwp-nav-menu {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding: 2rem 0;
        transition: left 0.3s ease;
        z-index: 999;
    }

    .breezwp-nav-menu.active {
        left: 0;
    }

    .breezwp-nav-list {
        flex-direction: column;
        gap: 1rem;
        width: 100%;
        text-align: center;
    }

    .breezwp-nav-link {
        padding: 1rem 2rem;
        width: 100%;
        display: block;
    }

    .breezwp-dropdown-menu {
        position: static;
        background: transparent;
        border: none;
        box-shadow: none;
        opacity: 1;
        visibility: visible;
        transform: none;
        padding: 0;
        margin-top: 0.5rem;
    }

    .breezwp-dropdown-menu a {
        padding: 0.5rem 2rem;
        font-size: 0.9rem;
    }

    .breezwp-nav-toggle {
        display: flex;
    }
}

@media (max-width: 768px) {
    .breezwp-nav-container {
        padding: 0 1rem;
    }

    .breezwp-logo a {
        font-size: 1.2rem;
    }

    .breezwp-logo i {
        font-size: 1.5rem;
    }
}

/* Page Header */
.breezwp-page-header {
    background: linear-gradient(135deg, rgba(144, 41, 167, 0.95) 0%, rgba(96, 45, 146, 0.95) 50%, rgba(59, 195, 212, 0.95) 100%),
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="20" cy="80" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="80" cy="20" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    background-size: cover, 100px 100px;
    background-position: center, 0 0;
    padding: 10rem 2rem 6rem;
    text-align: center;
    margin-top: 80px;
    position: relative;
    overflow: hidden;
}

.breezwp-page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" preserveAspectRatio="none"><polygon fill="rgba(255,255,255,0.1)" points="1000,100 1000,0 0,100"/></svg>');
    background-size: 100% 100%;
    pointer-events: none;
}

.breezwp-page-header::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: breezwp-float 6s ease-in-out infinite;
    pointer-events: none;
}

.breezwp-page-header-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    margin: 0 auto;
}

.breezwp-page-header-content h1 {
    font-family: 'Open Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: clamp(3rem, 6vw, 4.5rem);
    font-weight: 800;
    color: #ffffff;
    margin-bottom: 1.5rem;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
    letter-spacing: -0.02em;
    animation: breezwp-fadeInUp 1s ease-out;
}

.breezwp-page-header-content p {
    font-size: 1.3rem;
    color: rgba(255, 255, 255, 0.9);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    animation: breezwp-fadeInUp 1s ease-out 0.2s both;
}

/* Premium Animations */
@keyframes breezwp-float {
    0%, 100% {
        transform: translate(-50%, -50%) translateY(0px);
    }
    50% {
        transform: translate(-50%, -50%) translateY(-20px);
    }
}

@keyframes breezwp-fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes breezwp-shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Enhanced CTA Section */
.breezwp-cta-section {
    background: linear-gradient(135deg, rgba(144, 41, 167, 0.05) 0%, rgba(96, 45, 146, 0.05) 50%, rgba(59, 195, 212, 0.05) 100%);
    padding: 6rem 2rem;
    border-radius: 30px;
    margin: 4rem 0;
    position: relative;
    overflow: hidden;
}

.breezwp-cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #9029a7, #602d92, #3bc3d4);
    border-radius: 30px 30px 0 0;
}

.breezwp-cta-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #343a40;
    margin-bottom: 1.5rem;
    text-align: center;
}

.breezwp-cta-content p {
    font-size: 1.1rem;
    color: #6c757d;
    text-align: center;
    max-width: 600px;
    margin: 0 auto 2.5rem;
    line-height: 1.6;
}

.breezwp-cta-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .breezwp-page-header {
        padding: 8rem 1rem 4rem;
    }

    .breezwp-page-header-content h1 {
        font-size: 2.5rem;
    }

    .breezwp-page-header-content p {
        font-size: 1.1rem;
    }

    .breezwp-rate-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .breezwp-rate-card,
    .breezwp-info-item {
        padding: 2rem;
    }

    .breezwp-availability-widget {
        padding: 1.5rem;
        margin: 1.5rem 0;
    }

    .breezwp-cta-section {
        padding: 4rem 1.5rem;
        margin: 3rem 0;
    }

    .breezwp-cta-content h2 {
        font-size: 2rem;
    }

    .breezwp-cta-buttons {
        flex-direction: column;
        gap: 1rem;
    }

    .breezwp-book-btn,
    .breezwp-secondary-btn {
        width: 100%;
        max-width: 280px;
        text-align: center;
    }
}

/* Booking Container Layout */
.breezwp-booking-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    margin-top: 3rem;
}

.breezwp-booking-widget {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 25px;
    padding: 2rem;
    border: 1px solid rgba(222, 226, 230, 0.3);
    backdrop-filter: blur(15px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: visible;
}

.breezwp-booking-widget::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #9029a7, #602d92, #3bc3d4);
    border-radius: 25px 25px 0 0;
}

.breezwp-booking-form-header {
    margin-bottom: 2rem;
    text-align: center;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid rgba(222, 226, 230, 0.3);
}

.breezwp-booking-form-header h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #343a40;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.breezwp-booking-form-header p {
    color: #6c757d;
    font-size: 1rem;
}

.breezwp-booking-sidebar {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.breezwp-currency-converter,
.breezwp-optional-fees {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 1.5rem;
    border: 1px solid rgba(222, 226, 230, 0.3);
    backdrop-filter: blur(15px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
}

.breezwp-currency-converter::before,
.breezwp-optional-fees::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #3bc3d4, #9029a7);
    border-radius: 20px 20px 0 0;
}

.breezwp-sidebar-header {
    margin-bottom: 1.5rem;
    text-align: center;
}

.breezwp-sidebar-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #343a40;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.breezwp-sidebar-header p {
    color: #6c757d;
    font-size: 0.9rem;
}

.breezwp-converter-widget iframe {
    border-radius: 15px;
    overflow: hidden;
}

/* Optional Fees Styling */
.breezwp-fees-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.breezwp-fee-item {
    display: flex;
    gap: 1rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 15px;
    border: 1px solid rgba(222, 226, 230, 0.3);
    transition: all 0.3s ease;
}

.breezwp-fee-item:hover {
    background: rgba(255, 255, 255, 0.95);
    border-color: rgba(144, 41, 167, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.breezwp-fee-icon {
    flex-shrink: 0;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #9029a7, #3bc3d4);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.breezwp-fee-details h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #343a40;
    margin-bottom: 0.5rem;
}

.breezwp-fee-details p {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0.8rem;
    line-height: 1.4;
}

.breezwp-fee-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: #9029a7;
    margin-bottom: 0.3rem;
}

.breezwp-fee-price span {
    font-size: 0.9rem;
    font-weight: 500;
    color: #6c757d;
}

.breezwp-fee-details small {
    color: #6c757d;
    font-size: 0.8rem;
    font-style: italic;
}

.breezwp-fees-note {
    background: rgba(59, 195, 212, 0.1);
    border: 1px solid rgba(59, 195, 212, 0.3);
    border-radius: 10px;
    padding: 1rem;
    display: flex;
    align-items: flex-start;
    gap: 0.8rem;
}

.breezwp-fees-note i {
    color: #3bc3d4;
    font-size: 1rem;
    margin-top: 0.2rem;
    flex-shrink: 0;
}

.breezwp-fees-note p {
    color: #495057;
    font-size: 0.9rem;
    line-height: 1.4;
    margin: 0;
}

/* Dynamic OwnerRez Widget Styling */
.ownerrez-container {
    overflow: visible;
    position: relative;
    height: auto;
}

.ownerrez-widget {
    min-height: 660px !important;
    height: auto !important;
    max-height: none !important;
    overflow: visible !important;
    position: relative;
}

/* Ensure booking widget content is always visible */
.breezwp-booking-widget .ownerrez-container {
    overflow: visible !important;
    height: auto !important;
    min-height: auto !important;
}

.breezwp-booking-widget .ownerrez-widget * {
    overflow: visible !important;
}

.breezwp-booking-widget iframe {
    overflow-y: auto !important;
    overflow-x: hidden !important;
}

/* Specific widget height adjustments */
.breezwp-availability-widget .ownerrez-widget {
    height: 600px !important;
    min-height: 600px !important;
    max-height: 600px !important;
}

.breezwp-reviews-widget .ownerrez-widget {
    height: 600px !important;
    min-height: 600px !important;
    max-height: 600px !important;
}

/* Booking widget uses dynamic sizing */
.breezwp-booking-widget .ownerrez-widget {
    min-height: 660px !important;
    height: auto !important;
    max-height: none !important;
    overflow: visible !important;
}

/* Ensure widget containers don't clip content */
.breezwp-availability-widget,
.breezwp-reviews-widget {
    overflow: visible;
    min-height: inherit;
}



/* Responsive Booking Layout */
@media (max-width: 1024px) {
    .breezwp-booking-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .breezwp-booking-sidebar {
        order: -1;
    }

    .breezwp-currency-converter,
    .breezwp-optional-fees {
        padding: 1.2rem;
    }
}

@media (max-width: 768px) {
    .breezwp-booking-container {
        gap: 1.5rem;
        margin-top: 2rem;
    }

    .breezwp-booking-widget,
    .breezwp-currency-converter,
    .breezwp-optional-fees {
        padding: 1.5rem;
        border-radius: 20px;
    }

    .breezwp-booking-form-header h3 {
        font-size: 1.3rem;
    }

    .breezwp-sidebar-header h3 {
        font-size: 1.1rem;
    }

    .breezwp-fee-item {
        padding: 1.2rem;
        gap: 0.8rem;
    }

    .breezwp-fee-icon {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }

    .breezwp-fee-details h4 {
        font-size: 1rem;
    }

    .breezwp-fee-price {
        font-size: 1.1rem;
    }

    .breezwp-converter-widget iframe {
        height: 400px !important;
    }
}

/* Responsive widget heights for mobile */
@media (max-width: 768px) {
    .ownerrez-widget {
        height: 600px !important;
        min-height: 600px !important;
        max-height: 600px !important;
    }

    .breezwp-availability-widget .ownerrez-widget {
        height: 600px !important;
        min-height: 600px !important;
        max-height: 600px !important;
    }

    .breezwp-reviews-widget .ownerrez-widget {
        height: 600px !important;
        min-height: 600px !important;
        max-height: 600px !important;
    }

    /* Keep booking widget dynamic on mobile too */
    .breezwp-booking-widget .ownerrez-widget {
        min-height: 600px !important;
        height: auto !important;
        max-height: none !important;
        overflow: visible !important;
    }
}

/* Target OwnerRez iframes specifically */
.ownerrez-widget iframe,
.ownerrez-widget > iframe,
.ownerrez-widget div[data-ownerrez-iframe],
.ownerrez-widget [data-ownerrez-iframe] {
    height: 400px !important;
    min-height: 400px !important;
    max-height: 400px !important;
    width: 100% !important;
    border: none !important;
    overflow: hidden !important;
}

/* Target any iframe within OwnerRez containers (excluding booking widget) */
.breezwp-availability-widget iframe,
.breezwp-reviews-widget iframe {
    height: 400px !important;
    min-height: 400px !important;
    max-height: 400px !important;
    width: 100% !important;
    border: none !important;
    overflow: hidden !important;
}

/* Booking widget iframes use dynamic sizing */
.breezwp-booking-widget iframe {
    min-height: 660px !important;
    height: auto !important;
    max-height: none !important;
    width: 100% !important;
    border: none !important;
    overflow: visible !important;
}

/* Target OwnerRez script-generated content */
.ownerrez-widget *,
.ownerrez-widget > * {
    max-height: 400px !important;
}

/* Force height on any dynamically created elements */
.ownerrez-widget [style*="height"],
.ownerrez-widget [style*="min-height"] {
    height: 600px !important;
    min-height: 600px !important;
    max-height: 600px !important;
}

/* Target HTML page availability widget specifically */
.ownerrez-widget[data-widget-type*="HTML page availability"] {
    height: 600px !important;
    min-height: 600px !important;
    max-height: 600px !important;
}

/* Target any div elements that OwnerRez might create */
.ownerrez-widget > div,
.ownerrez-widget div[class*="ownerrez"],
.ownerrez-widget div[class*="calendar"],
.ownerrez-widget div[class*="availability"] {
    height: 600px !important;
    min-height: 600px !important;
    max-height: 600px !important;
    overflow: hidden !important;
}

/* Target any table elements that might be created */
.ownerrez-widget table,
.ownerrez-widget table * {
    max-height: 600px !important;
}

/* Target any scrollable containers */
.ownerrez-widget [style*="overflow"],
.ownerrez-widget [style*="scroll"] {
    max-height: 600px !important;
    overflow: hidden !important;
} 