<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Check Availability - <PERSON>z Villa</title>
  <link rel="stylesheet" href="breezvillas.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700;800&display=swap"
    rel="stylesheet">

  <!-- Google Analytics Code Placeholder -->
  <!-- Replace with your actual Google Analytics code -->
  <!-- <script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script> -->

  <!-- Facebook Pixel Code Placeholder -->
  <!-- Replace with your actual Facebook Pixel code -->
  <!-- <script>!function(f,b,e,v,n,t,s)...</script> -->

  <!-- Microsoft Clarity Analytics Code Placeholder -->
  <!-- Replace with your actual Microsoft Clarity code -->
  <!-- <script type="text/javascript">(function(c,l,a,r,i,t,y){...})</script> -->

  <!-- Chatbot Code Placeholder -->
  <!-- Replace with your actual chatbot code -->
</head>

<body>
  <!-- Header Navigation -->
  <header class="breezwp-header">
    <nav class="breezwp-nav">
      <div class="breezwp-nav-container">
        <div class="breezwp-logo">
          <a href="index.html">
            <i class="fas fa-home"></i>
            <span>Breez Villa</span>
          </a>
        </div>

        <div class="breezwp-nav-menu" id="nav-menu">
          <ul class="breezwp-nav-list">
            <li><a href="index.html" class="breezwp-nav-link">Home</a></li>
            <li class="breezwp-dropdown">
              <a href="#" class="breezwp-nav-link">Availability <i class="fas fa-chevron-down"></i></a>
              <ul class="breezwp-dropdown-menu">
                <li><a href="availability.html">Check Availability</a></li>
                <li><a href="calendar.html">Calendar View</a></li>
              </ul>
            </li>
            <li><a href="photos.html" class="breezwp-nav-link">Photos</a></li>
            <li><a href="reviews.html" class="breezwp-nav-link">Reviews</a></li>
            <li class="breezwp-dropdown">
              <a href="#" class="breezwp-nav-link">Amenities <i class="fas fa-chevron-down"></i></a>
              <ul class="breezwp-dropdown-menu">
                <li><a href="bedrooms.html">Bedrooms</a></li>
                <li><a href="pool.html">Pool</a></li>
                <li><a href="gameroom.html">Gameroom</a></li>
                <li><a href="kitchen-dining.html">Kitchen & Dining</a></li>
                <li><a href="resort-amenities.html">Resort Amenities</a></li>
              </ul>
            </li>
            <li><a href="location.html" class="breezwp-nav-link">Location</a></li>
            <li><a href="faq.html" class="breezwp-nav-link">FAQ</a></li>
            <li><a href="blog.html" class="breezwp-nav-link">Blog</a></li>
            <li><a href="book.html" class="breezwp-nav-link breezwp-book-now">Book Now</a></li>
          </ul>
        </div>

        <div class="breezwp-nav-toggle" id="nav-toggle">
          <span class="bar"></span>
          <span class="bar"></span>
          <span class="bar"></span>
        </div>
      </div>
    </nav>
  </header>

  <!-- Page Header -->
  <section class="breezwp-page-header">
    <div class="breezwp-page-header-content">
      <h1>Check Availability</h1>
      <p>Discover your perfect dates at our luxury villa and experience the ultimate Orlando getaway</p>
    </div>
  </section>

  <!-- Main Content -->
  <main class="breezwp-main-content">
    <!-- Availability Widget -->
    <section class="breezwp-section breezwp-section-featured">
      <div class="breezwp-section-header">
        <div class="breezwp-icon-wrapper">
          <i class="fas fa-calendar-check"></i>
        </div>
        <h2>Real-Time Availability</h2>
        <p>Check available dates and rates for your dream vacation</p>
      </div>

      <div class="breezwp-availability-widget">
        <!-- OwnerRez Multiple Month Calendar widget for Breez Villa at Solterra Resort -->
        <div class="ownerrez-widget" data-propertyId="9ce7dc1ccb45420199427773bff33387"
          data-widget-type="HTML page availability - Multiple Month Calendar"
          data-widgetId="20454d5e45134e729af06ad85b91a4d1"></div>
        <script src="https://app.ownerrez.com/widget.js"></script>
      </div>

      <div class="breezwp-section-cta">
        <a href="book.html" class="breezwp-book-btn">Book Your Dates</a>
      </div>
    </section>

    <!-- Availability Information -->
    <section class="breezwp-section">
      <div class="breezwp-section-header">
        <div class="breezwp-icon-wrapper">
          <i class="fas fa-info-circle"></i>
        </div>
        <h2>Booking Information</h2>
        <p>Everything you need to know about reserving your stay</p>
      </div>

      <div class="breezwp-info-grid">
        <div class="breezwp-info-item">
          <i class="fas fa-clock"></i>
          <h3>Check-in & Check-out Times</h3>
          <p><strong>Check-in:</strong> 4:00 PM</p>
          <p><strong>Check-out:</strong> 10:00 AM</p>
          <p>Early check-in and late check-out may be available upon request</p>
        </div>

        <div class="breezwp-info-item">
          <i class="fas fa-calendar-alt"></i>
          <h3>Minimum Stay Requirements</h3>
          <p><strong>Standard:</strong> 3-night minimum stay</p>
          <p><strong>Peak Season:</strong> 5-night minimum stay</p>
          <p><strong>Holidays:</strong> 7-night minimum stay</p>
        </div>

        <div class="breezwp-info-item">
          <i class="fas fa-credit-card"></i>
          <h3>Payment Terms</h3>
          <p><strong>Deposit:</strong> 20% required to confirm booking</p>
          <p><strong>Final Payment:</strong> Due 60 days before arrival</p>
          <p><strong>Accepted:</strong> All major credit cards, bank transfers</p>
          <p>Secure payment processing with SSL encryption</p>
        </div>

        <div class="breezwp-info-item">
          <i class="fas fa-shield-alt"></i>
          <h3>Cancellation Policy</h3>
          <p><strong>90+ days:</strong> 100% refund (less $50 processing fee)</p>
          <p><strong>60-90 days:</strong> 50% refund (less $50 processing fee)</p>
          <p><strong>Within 60 days:</strong> No refund</p>
          <p><strong>Grace Period:</strong> Free cancellation within 24 hours of booking if arrival is 72+ hours away</p>
          <p><em>Travel insurance is highly recommended</em></p>
        </div>
      </div>
    </section>

    <!-- Seasonal Rates -->
    <section class="breezwp-section breezwp-section-featured">
      <div class="breezwp-section-header">
        <div class="breezwp-icon-wrapper">
          <i class="fas fa-chart-line"></i>
        </div>
        <h2>Seasonal Rates</h2>
        <p>Our rates vary by season to offer the best value year-round</p>
      </div>

      <div class="breezwp-rate-grid">
        <div class="breezwp-rate-card">
          <h3>Peak Season</h3>
          <p class="breezwp-rate-period">March - August</p>
          <p class="breezwp-rate-description">Disney World peak season with highest demand and perfect weather for theme parks</p>
          <div class="breezwp-rate-features">
            <span><i class="fas fa-star"></i> Premium luxury experience</span>
            <span><i class="fas fa-swimming-pool"></i> Full resort amenities access</span>
            <span><i class="fas fa-sun"></i> Perfect weather conditions</span>
            <span><i class="fas fa-calendar-check"></i> Advanced booking recommended</span>
            <span><i class="fas fa-trophy"></i> Highest service standards</span>
          </div>
        </div>

        <div class="breezwp-rate-card">
          <h3>Shoulder Season</h3>
          <p class="breezwp-rate-period">September - November</p>
          <p class="breezwp-rate-description">Ideal balance of great weather, moderate crowds, and excellent value</p>
          <div class="breezwp-rate-features">
            <span><i class="fas fa-leaf"></i> Beautiful fall weather</span>
            <span><i class="fas fa-swimming-pool"></i> Full resort amenities access</span>
            <span><i class="fas fa-users"></i> Smaller crowds at attractions</span>
            <span><i class="fas fa-dollar-sign"></i> Excellent value pricing</span>
            <span><i class="fas fa-heart"></i> Guest favorite season</span>
          </div>
        </div>

        <div class="breezwp-rate-card">
          <h3>Value Season</h3>
          <p class="breezwp-rate-period">December - February</p>
          <p class="breezwp-rate-description">Cooler temperatures with the best rates and exclusive winter experiences</p>
          <div class="breezwp-rate-features">
            <span><i class="fas fa-snowflake"></i> Mild winter weather</span>
            <span><i class="fas fa-swimming-pool"></i> Heated pool & spa access</span>
            <span><i class="fas fa-gift"></i> Holiday decorations at parks</span>
            <span><i class="fas fa-piggy-bank"></i> Best value rates</span>
            <span><i class="fas fa-calendar-plus"></i> Easy availability</span>
          </div>
        </div>
      </div>
    </section>

    <!-- Premium Features -->
    <section class="breezwp-section">
      <div class="breezwp-section-header">
        <div class="breezwp-icon-wrapper">
          <i class="fas fa-crown"></i>
        </div>
        <h2>Why Choose Breez Villa</h2>
        <p>Experience luxury, comfort, and convenience in our premium vacation rental</p>
      </div>

      <div class="breezwp-included-grid">
        <div class="breezwp-included-item">
          <i class="fas fa-home"></i>
          <span>Luxury 6-bedroom villa with premium furnishings</span>
        </div>

        <div class="breezwp-included-item">
          <i class="fas fa-swimming-pool"></i>
          <span>Private heated pool & spa with pool deck</span>
        </div>

        <div class="breezwp-included-item">
          <i class="fas fa-gamepad"></i>
          <span>Dedicated game room with arcade games</span>
        </div>

        <div class="breezwp-included-item">
          <i class="fas fa-utensils"></i>
          <span>Gourmet kitchen with high-end appliances</span>
        </div>

        <div class="breezwp-included-item">
          <i class="fas fa-car"></i>
          <span>Free parking for multiple vehicles</span>
        </div>

        <div class="breezwp-included-item">
          <i class="fas fa-wifi"></i>
          <span>High-speed WiFi throughout the property</span>
        </div>

        <div class="breezwp-included-item">
          <i class="fas fa-shield-alt"></i>
          <span>24/7 guest support and concierge services</span>
        </div>

        <div class="breezwp-included-item">
          <i class="fas fa-map-marker-alt"></i>
          <span>Prime location near Disney World & attractions</span>
        </div>

        <div class="breezwp-included-item">
          <i class="fas fa-dumbbell"></i>
          <span>Resort fitness center and amenities access</span>
        </div>

        <div class="breezwp-included-item">
          <i class="fas fa-baby"></i>
          <span>Family-friendly with cribs and high chairs available</span>
        </div>

        <div class="breezwp-included-item">
          <i class="fas fa-tv"></i>
          <span>Smart TVs in every bedroom and living area</span>
        </div>

        <div class="breezwp-included-item">
          <i class="fas fa-snowflake"></i>
          <span>Central air conditioning and heating</span>
        </div>
      </div>
    </section>

    <!-- Call to Action -->
    <section class="breezwp-cta-section">
      <div class="breezwp-cta-content">
        <h2>Ready to Experience Luxury?</h2>
        <p>Don't wait - our premium villa books quickly! Use the availability calendar above to secure your perfect dates, or contact our concierge team for personalized assistance with special requests and group bookings.</p>
        <div class="breezwp-cta-buttons">
          <a href="book.html" class="breezwp-book-btn">
            <i class="fas fa-calendar-check"></i> Book Your Dates
          </a>
          <a href="contact.html" class="breezwp-secondary-btn">
            <i class="fas fa-concierge-bell"></i> Contact Concierge
          </a>
        </div>
      </div>
    </section>
  </main>

  <!-- Footer -->
  <footer class="breezwp-footer">
    <div class="breezwp-footer-content">
      <div class="breezwp-footer-grid">
        <div class="breezwp-footer-section">
          <h3>Breez Villa</h3>
          <p>Luxury vacation rental at Solterra Resort, Orlando</p>
          <div class="breezwp-social-links">
            <a href="#" aria-label="Facebook"><i class="fab fa-facebook"></i></a>
            <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
            <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
          </div>
        </div>

        <div class="breezwp-footer-section">
          <h4>Quick Links</h4>
          <ul>
            <li><a href="index.html">Home</a></li>
            <li><a href="availability.html">Availability</a></li>
            <li><a href="photos.html">Photos</a></li>
            <li><a href="book.html">Book Now</a></li>
          </ul>
        </div>

        <div class="breezwp-footer-section">
          <h4>Amenities</h4>
          <ul>
            <li><a href="bedrooms.html">Bedrooms</a></li>
            <li><a href="pool.html">Pool</a></li>
            <li><a href="gameroom.html">Game Room</a></li>
            <li><a href="resort-amenities.html">Resort</a></li>
          </ul>
        </div>

        <div class="breezwp-footer-section">
          <h4>Contact</h4>
          <p><i class="fas fa-map-marker-alt"></i> Solterra Resort, Orlando, FL</p>
          <p><i class="fas fa-phone"></i> <a href="tel:+1234567890">+1 (234) 567-890</a></p>
          <p><i class="fas fa-envelope"></i> <a href="mailto:<EMAIL>"><EMAIL></a></p>
        </div>
      </div>

      <div class="breezwp-footer-bottom">
        <p>&copy; 2025 Breez Villa. All rights reserved. | Luxury Redefined.</p>
        <div class="breezwp-footer-links">
          <a href="privacy.html">Privacy Policy</a>
          <a href="terms.html">Terms of Service</a>
        </div>
      </div>
    </div>
  </footer>

  <!-- JavaScript for Navigation -->
  <script>
    // Mobile Navigation Toggle
    const navToggle = document.getElementById('nav-toggle');
    const navMenu = document.getElementById('nav-menu');

    navToggle.addEventListener('click', () => {
      navMenu.classList.toggle('active');
      navToggle.classList.toggle('active');
    });

    // Close mobile menu when clicking on a link
    document.querySelectorAll('.breezwp-nav-link').forEach(link => {
      link.addEventListener('click', () => {
        navMenu.classList.remove('active');
        navToggle.classList.remove('active');
      });
    });

    // Dropdown functionality
    document.querySelectorAll('.breezwp-dropdown').forEach(dropdown => {
      dropdown.addEventListener('mouseenter', () => {
        dropdown.querySelector('.breezwp-dropdown-menu').style.display = 'block';
      });

      dropdown.addEventListener('mouseleave', () => {
        dropdown.querySelector('.breezwp-dropdown-menu').style.display = 'none';
      });
    });

         // OwnerRez Widget Height Enforcement
     function enforceOwnerRezHeight() {
       const widgets = document.querySelectorAll('.ownerrez-widget');
       widgets.forEach(widget => {
         // Force widget height
         widget.style.height = '600px';
         widget.style.minHeight = '600px';
         widget.style.maxHeight = '600px';
         widget.style.overflow = 'hidden';

         // Force iframe heights
         const iframes = widget.querySelectorAll('iframe');
         iframes.forEach(iframe => {
           iframe.style.height = '600px';
           iframe.style.minHeight = '600px';
           iframe.style.maxHeight = '600px';
           iframe.style.overflow = 'hidden';
         });

         // Force any other elements
         const allElements = widget.querySelectorAll('*');
         allElements.forEach(element => {
           if (element.style.height || element.style.minHeight) {
             element.style.height = '600px';
             element.style.minHeight = '600px';
             element.style.maxHeight = '600px';
             element.style.overflow = 'hidden';
           }
         });
       });
     }

         // Run height enforcement after page load and periodically
     document.addEventListener('DOMContentLoaded', enforceOwnerRezHeight);
     window.addEventListener('load', enforceOwnerRezHeight);

     // Check more frequently for dynamically loaded content
     setInterval(enforceOwnerRezHeight, 500);
     
     // Run immediately and then at various intervals
     setTimeout(enforceOwnerRezHeight, 100);
     setTimeout(enforceOwnerRezHeight, 500);
     setTimeout(enforceOwnerRezHeight, 1000);
     setTimeout(enforceOwnerRezHeight, 2000);
     setTimeout(enforceOwnerRezHeight, 3000);
     setTimeout(enforceOwnerRezHeight, 5000);
     setTimeout(enforceOwnerRezHeight, 10000);
     
     // Also run when the page becomes visible (in case of tab switching)
     document.addEventListener('visibilitychange', () => {
       if (!document.hidden) {
         enforceOwnerRezHeight();
       }
     });
  </script>
</body>

</html>