<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Photo Gallery - Breez Villa</title>
    <link rel="stylesheet" href="breezvillas.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Google Analytics Code Placeholder -->
    <!-- Replace with your actual Google Analytics code -->
    <!-- <script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script> -->
    
    <!-- Facebook Pixel Code Placeholder -->
    <!-- Replace with your actual Facebook Pixel code -->
    <!-- <script>!function(f,b,e,v,n,t,s)...</script> -->
    
    <!-- Microsoft Clarity Analytics Code Placeholder -->
    <!-- Replace with your actual Microsoft Clarity code -->
    <!-- <script type="text/javascript">(function(c,l,a,r,i,t,y){...})</script> -->
    
    <!-- Chatbot Code Placeholder -->
    <!-- Replace with your actual chatbot code -->
</head>
<body>
    <!-- Header Navigation -->
    <header class="breezwp-header">
        <nav class="breezwp-nav">
            <div class="breezwp-nav-container">
                <div class="breezwp-logo">
                    <a href="index.html">
                        <i class="fas fa-home"></i>
                        <span>Breez Villa</span>
                    </a>
                </div>
                
                <div class="breezwp-nav-menu" id="nav-menu">
                    <ul class="breezwp-nav-list">
                        <li><a href="index.html" class="breezwp-nav-link">Home</a></li>
                        <li class="breezwp-dropdown">
                            <a href="#" class="breezwp-nav-link">Availability <i class="fas fa-chevron-down"></i></a>
                            <ul class="breezwp-dropdown-menu">
                                <li><a href="availability.html">Check Availability</a></li>
                                <li><a href="calendar.html">Calendar View</a></li>
                            </ul>
                        </li>
                        <li><a href="photos.html" class="breezwp-nav-link">Photos</a></li>
                        <li><a href="reviews.html" class="breezwp-nav-link">Reviews</a></li>
                        <li class="breezwp-dropdown">
                            <a href="#" class="breezwp-nav-link">Amenities <i class="fas fa-chevron-down"></i></a>
                            <ul class="breezwp-dropdown-menu">
                                <li><a href="bedrooms.html">Bedrooms</a></li>
                                <li><a href="pool.html">Pool</a></li>
                                <li><a href="gameroom.html">Gameroom</a></li>
                                <li><a href="kitchen-dining.html">Kitchen & Dining</a></li>
                                <li><a href="resort-amenities.html">Resort Amenities</a></li>
                            </ul>
                        </li>
                        <li><a href="location.html" class="breezwp-nav-link">Location</a></li>
                        <li><a href="faq.html" class="breezwp-nav-link">FAQ</a></li>
                        <li><a href="blog.html" class="breezwp-nav-link">Blog</a></li>
                        <li><a href="book.html" class="breezwp-nav-link breezwp-book-now">Book Now</a></li>
                    </ul>
                </div>
                
                <div class="breezwp-nav-toggle" id="nav-toggle">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Page Header -->
    <section class="breezwp-page-header">
        <div class="breezwp-page-header-content">
            <h1>Photo Gallery</h1>
            <p>Explore Breez Villa through stunning photography</p>
        </div>
    </section>

    <!-- Main Content -->
    <main class="breezwp-main-content">
        <!-- Gallery Navigation -->
        <section class="breezwp-section">
            <div class="breezwp-gallery-nav">
                <button class="breezwp-gallery-filter active" data-filter="all">All Photos</button>
                <button class="breezwp-gallery-filter" data-filter="exterior">Exterior</button>
                <button class="breezwp-gallery-filter" data-filter="bedrooms">Bedrooms</button>
                <button class="breezwp-gallery-filter" data-filter="bathrooms">Bathrooms</button>
                <button class="breezwp-gallery-filter" data-filter="kitchen">Kitchen & Dining</button>
                <button class="breezwp-gallery-filter" data-filter="living">Living Areas</button>
                <button class="breezwp-gallery-filter" data-filter="pool">Pool & Outdoor</button>
                <button class="breezwp-gallery-filter" data-filter="gameroom">Game Room</button>
                <button class="breezwp-gallery-filter" data-filter="resort">Resort Amenities</button>
            </div>
        </section>

        <!-- Photo Gallery -->
        <section class="breezwp-section">
            <div class="breezwp-carousel-container">
                <!-- Main Carousel -->
                <div class="breezwp-carousel-main">
                    <button class="breezwp-carousel-nav breezwp-carousel-prev" aria-label="Previous photo">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    
                    <div class="breezwp-carousel-slide">
                        <img src="" alt="" class="breezwp-carousel-image" id="main-image">
                        <div class="breezwp-carousel-caption">
                            <h3 id="main-title"></h3>
                            <p id="main-description"></p>
                        </div>
                    </div>

                    <button class="breezwp-carousel-nav breezwp-carousel-next" aria-label="Next photo">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
                
                <!-- Thumbnail Navigation -->
                <div class="breezwp-carousel-thumbnails" id="thumbnails">
                    <!-- Thumbnails will be populated by JavaScript -->
                </div>
                
                <!-- Carousel Controls -->
                <div class="breezwp-carousel-controls">
                    <button class="breezwp-carousel-play-pause" id="play-pause" aria-label="Play/Pause slideshow">
                        <i class="fas fa-play"></i>
                    </button>
                    <span class="breezwp-carousel-counter">
                        <span id="current-slide">1</span> / <span id="total-slides">0</span>
                    </span>
                </div>
            </div>
        </section>

        <!-- Call to Action -->
        <section class="breezwp-cta-section">
            <div class="breezwp-cta-content">
                <h2>Ready to Experience Breez Villa?</h2>
                <p>Book your stay and enjoy all these amazing amenities in person.</p>
                <div class="breezwp-cta-buttons">
                    <a href="book.html" class="breezwp-book-btn">Book Your Stay</a>
                    <a href="availability.html" class="breezwp-secondary-btn">Check Availability</a>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="breezwp-footer">
        <div class="breezwp-footer-content">
            <div class="breezwp-footer-grid">
                <div class="breezwp-footer-section">
                    <h3>Breez Villa</h3>
                    <p>Family vacation rental at Solterra Resort, Orlando</p>
                    <div class="breezwp-social-links">
                        <a href="#" aria-label="Facebook"><i class="fab fa-facebook"></i></a>
                        <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                        <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                    </div>
                </div>
                
                <div class="breezwp-footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="availability.html">Availability</a></li>
                        <li><a href="photos.html">Photos</a></li>
                        <li><a href="book.html">Book Now</a></li>
                    </ul>
                </div>
                
                <div class="breezwp-footer-section">
                    <h4>Amenities</h4>
                    <ul>
                        <li><a href="bedrooms.html">Bedrooms</a></li>
                        <li><a href="pool.html">Pool</a></li>
                        <li><a href="gameroom.html">Game Room</a></li>
                        <li><a href="resort-amenities.html">Resort</a></li>
                    </ul>
                </div>
                
                <div class="breezwp-footer-section">
                    <h4>Contact</h4>
                    <p><i class="fas fa-map-marker-alt"></i> Solterra Resort, Orlando, FL</p>
                    <p><i class="fas fa-phone"></i> <a href="tel:+14078018850">******-801-8850</a> (Call, Text / WhatsApp)</p>
                    <p><i class="fas fa-envelope"></i> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                </div>
            </div>
            
            <div class="breezwp-footer-bottom">
                <p>&copy; 2025 Breez Villa. All rights reserved. | Luxury Redefined.</p>
                <div class="breezwp-footer-links">
                    <a href="privacy.html">Privacy Policy</a>
                    <a href="terms.html">Terms of Service</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript for Navigation and Gallery -->
    <script>
        // Photo data array
        const photos = [
            // Exterior
            { src: 'images/Exerior_twilight.png', alt: 'Breez Villa Exterior at Twilight', title: 'Exterior at Twilight', description: 'Beautiful evening view of Breez Villa', category: 'exterior' },
            { src: 'images/Exterior_2.jpg', alt: 'Breez Villa Exterior', title: 'Exterior View', description: 'Stunning exterior of our luxury villa', category: 'exterior' },
            { src: 'images/Exterior.jpg', alt: 'Breez Villa Front View', title: 'Front View', description: 'Welcoming entrance to Breez Villa', category: 'exterior' },
            
            // Bedrooms
            { src: 'images/Sunshine_MasterBR_1.jpg', alt: 'Sunshine Master Bedroom', title: 'Sunshine Master Bedroom', description: 'Luxurious master suite with king bed', category: 'bedrooms' },
            { src: 'images/Sunshine_MasterBR_3.jpg', alt: 'Sunshine Master Bedroom View', title: 'Master Bedroom View', description: 'Spacious master bedroom layout', category: 'bedrooms' },
            { src: 'images/Sunshine_MasterBR_4.jpg', alt: 'Sunshine Master Bedroom Detail', title: 'Master Bedroom Detail', description: 'Elegant bedroom furnishings', category: 'bedrooms' },
            { src: 'images/Sunshine_MasterBR_5.jpg', alt: 'Sunshine Master Bedroom Features', title: 'Master Bedroom Features', description: 'Premium amenities and comfort', category: 'bedrooms' },
            { src: 'images/Fleur _de_Sel_Suite_kingBed_1.jpg', alt: 'Fleur de Sel Suite King Bed', title: 'Fleur de Sel Suite', description: 'King bed suite with elegant design', category: 'bedrooms' },
            { src: 'images/Fleur _de_Sel_Suite_kingBed_2.jpg', alt: 'Fleur de Sel Suite View', title: 'Suite Layout', description: 'Spacious suite with premium amenities', category: 'bedrooms' },
            { src: 'images/Fleur _de_Sel_Suite_kingBed_3.jpg', alt: 'Fleur de Sel Suite Features', title: 'Suite Features', description: 'Luxury suite with attention to detail', category: 'bedrooms' },
            { src: 'images/Mickey_BR_1.jpg', alt: 'Mickey Themed Bedroom', title: 'Mickey Themed Room', description: 'Fun Disney-themed bedroom for kids', category: 'bedrooms' },
            { src: 'images/Mickey_BR_2.jpg', alt: 'Mickey Bedroom View', title: 'Mickey Room View', description: 'Perfect for young Disney fans', category: 'bedrooms' },
            { src: 'images/Mickey_BR_3.jpg', alt: 'Mickey Bedroom Features', title: 'Mickey Room Features', description: 'Magical Disney experience', category: 'bedrooms' },
            { src: 'images/Minion_BR_1.jpg', alt: 'Minion Themed Bedroom', title: 'Minion Themed Room', description: 'Fun Minion-themed bedroom', category: 'bedrooms' },
            { src: 'images/Minion_BR_2.jpg', alt: 'Minion Bedroom View', title: 'Minion Room View', description: 'Perfect for Minion fans', category: 'bedrooms' },
            { src: 'images/Mojito_Bedroom_1.jpg', alt: 'Mojito Bedroom', title: 'Mojito Bedroom', description: 'Comfortable guest bedroom', category: 'bedrooms' },
            { src: 'images/Mojito_Bedroom_2.jpg', alt: 'Mojito Bedroom View', title: 'Mojito Room View', description: 'Spacious guest accommodations', category: 'bedrooms' },
            { src: 'images/Mojito_Bedroom_3.jpg', alt: 'Mojito Bedroom Features', title: 'Mojito Room Features', description: 'Guest room with premium comfort', category: 'bedrooms' },
            
            // Bathrooms
            { src: 'images/MasterBathroom_1.jpg', alt: 'Master Bathroom', title: 'Master Bathroom', description: 'Luxurious master ensuite', category: 'bathrooms' },
            { src: 'images/MasterBathroom_2.jpg', alt: 'Master Bathroom Features', title: 'Master Bath Features', description: 'Premium bathroom amenities', category: 'bathrooms' },
            { src: 'images/MasterBathroom_3.jpg', alt: 'Master Bathroom Layout', title: 'Master Bath Layout', description: 'Spacious bathroom design', category: 'bathrooms' },
            { src: 'images/Bathroom2_1.jpg', alt: 'Second Bathroom', title: 'Second Bathroom', description: 'Family bathroom with tub/shower', category: 'bathrooms' },
            { src: 'images/Bathroom2_2.jpg', alt: 'Second Bathroom View', title: 'Second Bath View', description: 'Convenient family bathroom', category: 'bathrooms' },
            { src: 'images/Bathroom3_1.jpg', alt: 'Third Bathroom', title: 'Third Bathroom', description: 'Additional bathroom convenience', category: 'bathrooms' },
            { src: 'images/Bathroom3_2.jpg', alt: 'Third Bathroom View', title: 'Third Bath View', description: 'Well-appointed bathroom', category: 'bathrooms' },
            { src: 'images/CommonBathroom_1.jpg', alt: 'Common Bathroom', title: 'Common Bathroom', description: 'Shared family bathroom', category: 'bathrooms' },
            { src: 'images/CommonBathroom_2.jpg', alt: 'Common Bathroom View', title: 'Common Bath View', description: 'Accessible family bathroom', category: 'bathrooms' },
            
            // Kitchen & Dining
            { src: 'images/Kitchen_1.jpg', alt: 'Kitchen Overview', title: 'Kitchen Overview', description: 'Fully equipped gourmet kitchen', category: 'kitchen' },
            { src: 'images/Kitchen_2.jpg', alt: 'Kitchen Appliances', title: 'Kitchen Appliances', description: 'Stainless steel appliances', category: 'kitchen' },
            { src: 'images/Kitchen_3.jpg', alt: 'Kitchen Island', title: 'Kitchen Island', description: 'Island seating for 4', category: 'kitchen' },
            { src: 'images/Kitchen_4.jpg', alt: 'Kitchen Features', title: 'Kitchen Features', description: 'Premium kitchen amenities', category: 'kitchen' },
            { src: 'images/Kitchen_5.jpg', alt: 'Kitchen Detail', title: 'Kitchen Detail', description: 'Attention to every detail', category: 'kitchen' },
            { src: 'images/Kitchen_7.jpg', alt: 'Kitchen Layout', title: 'Kitchen Layout', description: 'Efficient kitchen design', category: 'kitchen' },
            { src: 'images/Kitchen_8-2.jpg', alt: 'Kitchen Storage', title: 'Kitchen Storage', description: 'Ample storage space', category: 'kitchen' },
            { src: 'images/Kitchen_8.jpg', alt: 'Kitchen Organization', title: 'Kitchen Organization', description: 'Well-organized kitchen space', category: 'kitchen' },
            { src: 'images/Kitchen_9.jpg', alt: 'Kitchen Finishing', title: 'Kitchen Finishing', description: 'Premium finishes throughout', category: 'kitchen' },
            { src: 'images/Kitchen-2.jpg', alt: 'Kitchen Alternative View', title: 'Kitchen Alternative View', description: 'Different perspective of kitchen', category: 'kitchen' },
            { src: 'images/Dining_1.jpg', alt: 'Dining Area', title: 'Dining Area', description: 'Dining table for 8 guests', category: 'kitchen' },
            { src: 'images/Dining_2.jpg', alt: 'Dining Setup', title: 'Dining Setup', description: 'Perfect for family meals', category: 'kitchen' },
            { src: 'images/Dining_3.jpg', alt: 'Dining Atmosphere', title: 'Dining Atmosphere', description: 'Welcoming dining environment', category: 'kitchen' },
            { src: 'images/CoffeeStation_2.jpg', alt: 'Coffee Station', title: 'Coffee Station', description: 'Premium coffee and tea selection', category: 'kitchen' },
            
            // Living Areas
            { src: 'images/Livingroom_10.jpg', alt: 'Living Room', title: 'Living Room', description: 'Open-concept living space', category: 'living' },
            { src: 'images/LivingRoom_3.jpg', alt: 'Living Room View', title: 'Living Room View', description: 'Comfortable seating area', category: 'living' },
            { src: 'images/LivingRoom_4.jpg', alt: 'Living Room Layout', title: 'Living Room Layout', description: 'Spacious family gathering area', category: 'living' },
            { src: 'images/LivingRoom_5-2.jpg', alt: 'Living Room Features', title: 'Living Room Features', description: 'Smart TV and entertainment', category: 'living' },
            { src: 'images/LivingRoom_5.jpg', alt: 'Living Room Detail', title: 'Living Room Detail', description: 'Elegant living room design', category: 'living' },
            { src: 'images/EntryWay_3.jpg', alt: 'Entry Way', title: 'Entry Way', description: 'Welcoming entrance area', category: 'living' },
            { src: 'images/Upstairs_Hallway.jpg', alt: 'Upstairs Hallway', title: 'Upstairs Hallway', description: 'Access to upstairs bedrooms', category: 'living' },
            
            // Pool & Outdoor
            { src: 'images/Pool_5.jpg', alt: 'Private Pool', title: 'Private Pool', description: 'Heated private pool', category: 'pool' },
            { src: 'images/Pool_BasketBall_1.jpg', alt: 'Pool Basketball', title: 'Pool Basketball', description: 'Pool basketball hoop for fun', category: 'pool' },
            { src: 'images/Pool_Sunset_2-2.jpg', alt: 'Pool at Sunset', title: 'Pool at Sunset', description: 'Beautiful sunset pool view', category: 'pool' },
            { src: 'images/Pool_Sunset_2-3.jpg', alt: 'Pool Sunset View', title: 'Pool Sunset View', description: 'Magical evening atmosphere', category: 'pool' },
            { src: 'images/Patio_Loungers_1.jpg', alt: 'Patio Loungers', title: 'Patio Loungers', description: 'Comfortable sun loungers', category: 'pool' },
            { src: 'images/Patio_Loungers_3.jpg', alt: 'Patio Setup', title: 'Patio Setup', description: 'Perfect outdoor relaxation', category: 'pool' },
            { src: 'images/Patio_Sunset_1.jpg', alt: 'Patio at Sunset', title: 'Patio at Sunset', description: 'Stunning sunset patio view', category: 'pool' },
            { src: 'images/Laundry.jpg', alt: 'Laundry Room', title: 'Laundry Room', description: 'Convenient laundry facilities', category: 'pool' },
            
            // Game Room
            { src: 'images/Gameroom_1.jpg', alt: 'Game Room Overview', title: 'Game Room Overview', description: 'Entertainment for all ages', category: 'gameroom' },
            { src: 'images/Gameroom_2.jpg', alt: 'Game Room Features', title: 'Game Room Features', description: 'Pool table and arcade machine', category: 'gameroom' },
            { src: 'images/Gameroom_3.jpg', alt: 'Game Room Layout', title: 'Game Room Layout', description: 'Spacious gaming area', category: 'gameroom' },
            { src: 'images/Gameroom_4.jpg', alt: 'Game Room Details', title: 'Game Room Details', description: 'Board games and entertainment', category: 'gameroom' },
            
            // Resort Amenities
            { src: 'images/Solterra_Clubhouse.jpg', alt: 'Solterra Clubhouse', title: 'Solterra Clubhouse', description: 'Resort clubhouse facilities', category: 'resort' },
            { src: 'images/Solterra_Clubhouse2.jpg', alt: 'Clubhouse Features', title: 'Clubhouse Features', description: 'Lounge and cyber café', category: 'resort' },
            { src: 'images/Solterra_LazyRiver.jpg', alt: 'Lazy River', title: 'Lazy River', description: 'Resort lazy river experience', category: 'resort' },
            { src: 'images/Solterra_PlayArea_1.jpg', alt: 'Children\'s Play Area', title: 'Children\'s Play Area', description: 'Safe play space for kids', category: 'resort' },
            { src: 'images/Solterra_PlayArea_2.jpg', alt: 'Play Area Features', title: 'Play Area Features', description: 'Fun activities for children', category: 'resort' },
            { src: 'images/Solterra_Resort_1.jpg', alt: 'Solterra Resort', title: 'Solterra Resort', description: 'Beautiful resort grounds', category: 'resort' },
            { src: 'images/Solterra_Resort-arielview.jpg', alt: 'Resort Aerial View', title: 'Resort Aerial View', description: 'Bird\'s eye view of Solterra', category: 'resort' },
            { src: 'images/Solterra_WaterSlide.jpg', alt: 'Water Slide', title: 'Water Slide', description: 'Exciting water slide', category: 'resort' },
            { src: 'images/Solterra-Resort-Fitness-Center.jpg', alt: 'Fitness Center', title: 'Fitness Center', description: 'Resort fitness facilities', category: 'resort' },
            { src: 'images/SolterraResort_Pool.jpg', alt: 'Resort Pool', title: 'Resort Pool', description: 'Community resort pool', category: 'resort' },
            { src: 'images/Solterra_Cabana.jpg', alt: 'Resort Cabana', title: 'Resort Cabana', description: 'Private cabana rentals', category: 'resort' }
        ];

        // Carousel variables
        let currentSlide = 0;
        let isPlaying = false;
        let slideshowInterval;
        let filteredPhotos = [...photos];

        // DOM elements
        const mainImage = document.getElementById('main-image');
        const mainTitle = document.getElementById('main-title');
        const mainDescription = document.getElementById('main-description');
        const thumbnailsContainer = document.getElementById('thumbnails');
        const currentSlideSpan = document.getElementById('current-slide');
        const totalSlidesSpan = document.getElementById('total-slides');
        const playPauseBtn = document.getElementById('play-pause');
        const prevBtn = document.querySelector('.breezwp-carousel-prev');
        const nextBtn = document.querySelector('.breezwp-carousel-next');

        // Initialize carousel
        function initCarousel() {
            updateMainImage();
            createThumbnails();
            updateCounter();
        }

        // Update main image display
        function updateMainImage() {
            if (filteredPhotos.length > 0) {
                const photo = filteredPhotos[currentSlide];
                mainImage.src = photo.src;
                mainImage.alt = photo.alt;
                mainTitle.textContent = photo.title;
                mainDescription.textContent = photo.description;
                
                // Update thumbnail active state
                updateThumbnailActiveState();
            }
        }

        // Create thumbnail navigation
        function createThumbnails() {
            thumbnailsContainer.innerHTML = '';
            
            filteredPhotos.forEach((photo, index) => {
                const thumbnail = document.createElement('div');
                thumbnail.className = 'breezwp-carousel-thumbnail';
                thumbnail.setAttribute('data-index', index);
                
                const img = document.createElement('img');
                img.src = photo.src;
                img.alt = photo.alt;
                
                thumbnail.appendChild(img);
                thumbnail.addEventListener('click', () => goToSlide(index));
                
                thumbnailsContainer.appendChild(thumbnail);
            });
        }

        // Update thumbnail active state
        function updateThumbnailActiveState() {
            const thumbnails = document.querySelectorAll('.breezwp-carousel-thumbnail');
            thumbnails.forEach((thumb, index) => {
                thumb.classList.toggle('active', index === currentSlide);
            });
        }

        // Update counter display
        function updateCounter() {
            currentSlideSpan.textContent = currentSlide + 1;
            totalSlidesSpan.textContent = filteredPhotos.length;
        }

        // Navigate to specific slide
        function goToSlide(index) {
            currentSlide = index;
            updateMainImage();
            updateCounter();
        }

        // Next slide
        function nextSlide() {
            currentSlide = (currentSlide + 1) % filteredPhotos.length;
            updateMainImage();
            updateCounter();
        }

        // Previous slide
        function prevSlide() {
            currentSlide = currentSlide === 0 ? filteredPhotos.length - 1 : currentSlide - 1;
            updateMainImage();
            updateCounter();
        }

        // Toggle slideshow
        function toggleSlideshow() {
            if (isPlaying) {
                stopSlideshow();
            } else {
                startSlideshow();
            }
        }

        // Start slideshow
        function startSlideshow() {
            isPlaying = true;
            playPauseBtn.innerHTML = '<i class="fas fa-pause"></i>';
            slideshowInterval = setInterval(nextSlide, 3000);
        }

        // Stop slideshow
        function stopSlideshow() {
            isPlaying = false;
            playPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
            clearInterval(slideshowInterval);
        }

        // Filter photos by category
        function filterPhotos(category) {
            if (category === 'all') {
                filteredPhotos = [...photos];
            } else {
                filteredPhotos = photos.filter(photo => photo.category === category);
            }
            
            currentSlide = 0;
            updateMainImage();
            createThumbnails();
            updateCounter();
            
            // Stop slideshow when filtering
            if (isPlaying) {
                stopSlideshow();
            }
        }

        // Event listeners
        prevBtn.addEventListener('click', prevSlide);
        nextBtn.addEventListener('click', nextSlide);
        playPauseBtn.addEventListener('click', toggleSlideshow);

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft') {
                prevSlide();
            } else if (e.key === 'ArrowRight') {
                nextSlide();
            } else if (e.key === ' ') {
                e.preventDefault();
                toggleSlideshow();
            }
        });

        // Filter button functionality
        const filterButtons = document.querySelectorAll('.breezwp-gallery-filter');
        filterButtons.forEach(button => {
            button.addEventListener('click', () => {
                // Remove active class from all buttons
                filterButtons.forEach(btn => btn.classList.remove('active'));
                // Add active class to clicked button
                button.classList.add('active');
                
                const filter = button.getAttribute('data-filter');
                filterPhotos(filter);
            });
        });

        // Initialize carousel when page loads
        document.addEventListener('DOMContentLoaded', initCarousel);

        // Mobile Navigation Toggle
        const navToggle = document.getElementById('nav-toggle');
        const navMenu = document.getElementById('nav-menu');
        
        navToggle.addEventListener('click', () => {
            navMenu.classList.toggle('active');
            navToggle.classList.toggle('active');
        });
        
        // Close mobile menu when clicking on a link
        document.querySelectorAll('.breezwp-nav-link').forEach(link => {
            link.addEventListener('click', () => {
                navMenu.classList.remove('active');
                navToggle.classList.remove('active');
            });
        });
        
        // Dropdown functionality
        document.querySelectorAll('.breezwp-dropdown').forEach(dropdown => {
            dropdown.addEventListener('mouseenter', () => {
                dropdown.querySelector('.breezwp-dropdown-menu').style.display = 'block';
            });
            
            dropdown.addEventListener('mouseleave', () => {
                dropdown.querySelector('.breezwp-dropdown-menu').style.display = 'none';
            });
        });
    </script>
</body>
</html>
