<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Guest Reviews - <PERSON><PERSON></title>
    <link rel="stylesheet" href="breezvillas.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Google Analytics Code Placeholder -->
    <!-- Replace with your actual Google Analytics code -->
    <!-- <script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script> -->
    
    <!-- Facebook Pixel Code Placeholder -->
    <!-- Replace with your actual Facebook Pixel code -->
    <!-- <script>!function(f,b,e,v,n,t,s)...</script> -->
    
    <!-- Microsoft Clarity Analytics Code Placeholder -->
    <!-- Replace with your actual Microsoft Clarity code -->
    <!-- <script type="text/javascript">(function(c,l,a,r,i,t,y){...})</script> -->
    
    <!-- Chatbot Code Placeholder -->
    <!-- Replace with your actual chatbot code -->
</head>
<body>
    <!-- Header Navigation -->
    <header class="breezwp-header">
        <nav class="breezwp-nav">
            <div class="breezwp-nav-container">
                <div class="breezwp-logo">
                    <a href="index.html">
                        <i class="fas fa-home"></i>
                        <span>Breez Villa</span>
                    </a>
                </div>
                
                <div class="breezwp-nav-menu" id="nav-menu">
                    <ul class="breezwp-nav-list">
                        <li><a href="index.html" class="breezwp-nav-link">Home</a></li>
                        <li class="breezwp-dropdown">
                            <a href="#" class="breezwp-nav-link">Availability <i class="fas fa-chevron-down"></i></a>
                            <ul class="breezwp-dropdown-menu">
                                <li><a href="availability.html">Check Availability</a></li>
                                <li><a href="calendar.html">Calendar View</a></li>
                            </ul>
                        </li>
                        <li><a href="photos.html" class="breezwp-nav-link">Photos</a></li>
                        <li><a href="reviews.html" class="breezwp-nav-link">Reviews</a></li>
                        <li class="breezwp-dropdown">
                            <a href="#" class="breezwp-nav-link">Amenities <i class="fas fa-chevron-down"></i></a>
                            <ul class="breezwp-dropdown-menu">
                                <li><a href="bedrooms.html">Bedrooms</a></li>
                                <li><a href="pool.html">Pool</a></li>
                                <li><a href="gameroom.html">Gameroom</a></li>
                                <li><a href="kitchen-dining.html">Kitchen & Dining</a></li>
                                <li><a href="resort-amenities.html">Resort Amenities</a></li>
                            </ul>
                        </li>
                        <li><a href="location.html" class="breezwp-nav-link">Location</a></li>
                        <li><a href="faq.html" class="breezwp-nav-link">FAQ</a></li>
                        <li><a href="blog.html" class="breezwp-nav-link">Blog</a></li>
                        <li><a href="book.html" class="breezwp-nav-link breezwp-book-now">Book Now</a></li>
                    </ul>
                </div>
                
                <div class="breezwp-nav-toggle" id="nav-toggle">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Page Header -->
    <section class="breezwp-page-header">
        <div class="breezwp-page-header-content">
            <h1>Guest Reviews</h1>
            <p>See what our guests say about their stay at Breez Villa</p>
        </div>
    </section>

    <!-- Main Content -->
    <main class="breezwp-main-content">
        <!-- Reviews Summary -->
        <section class="breezwp-section breezwp-section-featured">
            <div class="breezwp-section-header">
                <div class="breezwp-icon-wrapper">
                    <i class="fas fa-star"></i>
                </div>
                <h2>What Our Guests Say</h2>
                <p>Real experiences from families who've stayed at Breez Villa</p>
            </div>
            
            <div class="breezwp-reviews-summary">
                <div class="breezwp-review-stats">
                    <div class="breezwp-review-stat">
                        <span class="breezwp-review-number">5.0</span>
                        <span class="breezwp-review-label">Overall Rating</span>
                        <div class="breezwp-stars">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                    </div>
                    
                    <div class="breezwp-review-stat">
                        <span class="breezwp-review-number">100+</span>
                        <span class="breezwp-review-label">Happy Guests</span>
                        <div class="breezwp-review-subtext">Families who loved their stay</div>
                    </div>
                    
                    <div class="breezwp-review-stat">
                        <span class="breezwp-review-number">98%</span>
                        <span class="breezwp-review-label">Would Return</span>
                        <div class="breezwp-review-subtext">Guests planning to come back</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Reviews Widget -->
        <section class="breezwp-section">
            <div class="breezwp-section-header">
                <div class="breezwp-icon-wrapper">
                    <i class="fas fa-comments"></i>
                </div>
                <h2>Guest Reviews</h2>
                <p>Read detailed reviews from our recent guests</p>
            </div>
            
            <div class="breezwp-reviews-widget">
                <div class="ownerrez-container" style="height: 400px; overflow: hidden;">
                    <!-- OwnerRez Reviews widget for Breez Villa at Solterra Resort -->
                    <div class="ownerrez-widget" data-propertyId="9ce7dc1ccb45420199427773bff33387" data-widget-type="Customer Reviews - Reviews" data-widgetId="b0427206f34343f49d208a7e5953666f"></div>
                    <script src="https://app.ownerrez.com/widget.js"></script>
                </div>
            </div>
        </section>

        <!-- Review Categories -->
        <section class="breezwp-section breezwp-section-featured">
            <div class="breezwp-section-header">
                <div class="breezwp-icon-wrapper">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <h2>Rating Breakdown</h2>
                <p>See how we perform across different aspects of your stay</p>
            </div>
            
            <div class="breezwp-rating-grid">
                <div class="breezwp-rating-item">
                    <div class="breezwp-rating-header">
                        <span class="breezwp-rating-label">Cleanliness</span>
                        <span class="breezwp-rating-score">5.0</span>
                    </div>
                    <div class="breezwp-rating-bar">
                        <div class="breezwp-rating-fill" style="width: 100%"></div>
                    </div>
                </div>
                
                <div class="breezwp-rating-item">
                    <div class="breezwp-rating-header">
                        <span class="breezwp-rating-label">Communication</span>
                        <span class="breezwp-rating-score">5.0</span>
                    </div>
                    <div class="breezwp-rating-bar">
                        <div class="breezwp-rating-fill" style="width: 100%"></div>
                    </div>
                </div>
                
                <div class="breezwp-rating-item">
                    <div class="breezwp-rating-header">
                        <span class="breezwp-rating-label">Check-in</span>
                        <span class="breezwp-rating-score">5.0</span>
                    </div>
                    <div class="breezwp-rating-bar">
                        <div class="breezwp-rating-fill" style="width: 100%"></div>
                    </div>
                </div>
                
                <div class="breezwp-rating-item">
                    <div class="breezwp-rating-header">
                        <span class="breezwp-rating-label">Accuracy</span>
                        <span class="breezwp-rating-score">5.0</span>
                    </div>
                    <div class="breezwp-rating-bar">
                        <div class="breezwp-rating-fill" style="width: 100%"></div>
                    </div>
                </div>
                
                <div class="breezwp-rating-item">
                    <div class="breezwp-rating-header">
                        <span class="breezwp-rating-label">Location</span>
                        <span class="breezwp-rating-score">5.0</span>
                    </div>
                    <div class="breezwp-rating-bar">
                        <div class="breezwp-rating-fill" style="width: 100%"></div>
                    </div>
                </div>
                
                <div class="breezwp-rating-item">
                    <div class="breezwp-rating-header">
                        <span class="breezwp-rating-label">Value</span>
                        <span class="breezwp-rating-score">5.0</span>
                    </div>
                    <div class="breezwp-rating-bar">
                        <div class="breezwp-rating-fill" style="width: 100%"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Featured Reviews -->
        <section class="breezwp-section">
            <div class="breezwp-section-header">
                <div class="breezwp-icon-wrapper">
                    <i class="fas fa-heart"></i>
                </div>
                <h2>Featured Guest Experiences</h2>
                <p>Highlights from some of our most memorable guest stays</p>
            </div>
            
            <div class="breezwp-featured-reviews">
                <div class="breezwp-featured-review">
                    <div class="breezwp-review-content">
                        <div class="breezwp-review-stars">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <p>"Absolutely perfect family vacation home! The themed rooms for the kids were a huge hit, and the pool was perfect for our family. Everything was spotless and the location near Disney was ideal. We'll definitely be back!"</p>
                        <div class="breezwp-review-author">
                            <strong>Sarah M.</strong> - Family of 6
                        </div>
                    </div>
                </div>
                
                <div class="breezwp-featured-review">
                    <div class="breezwp-review-content">
                        <div class="breezwp-review-stars">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <p>"The game room was amazing - our teenagers loved it! The kitchen was fully equipped for cooking family meals, and the resort amenities were fantastic. The owners thought of everything for families."</p>
                        <div class="breezwp-review-author">
                            <strong>Mike & Lisa T.</strong> - Family of 8
                        </div>
                    </div>
                </div>
                
                <div class="breezwp-featured-review">
                    <div class="breezwp-review-content">
                        <div class="breezwp-review-stars">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                        <p>"We've stayed in many vacation rentals, but Breez Villa exceeded all expectations. The attention to detail, cleanliness, and family-friendly amenities made our Disney vacation truly special."</p>
                        <div class="breezwp-review-author">
                            <strong>Jennifer & David R.</strong> - Family of 5
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Call to Action -->
        <section class="breezwp-cta-section">
            <div class="breezwp-cta-content">
                <h2>Ready to Create Your Own 5-Star Experience?</h2>
                <p>Join the hundreds of happy families who've made Breez Villa their home away from home in Orlando.</p>
                <div class="breezwp-cta-buttons">
                    <a href="book.html" class="breezwp-book-btn">Book Your Stay</a>
                    <a href="availability.html" class="breezwp-secondary-btn">Check Availability</a>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="breezwp-footer">
        <div class="breezwp-footer-content">
            <div class="breezwp-footer-grid">
                <div class="breezwp-footer-section">
                    <h3>Breez Villa</h3>
                    <p>Family vacation rental at Solterra Resort, Orlando</p>
                    <div class="breezwp-social-links">
                        <a href="#" aria-label="Facebook"><i class="fab fa-facebook"></i></a>
                        <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                        <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                    </div>
                </div>
                
                <div class="breezwp-footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="availability.html">Availability</a></li>
                        <li><a href="photos.html">Photos</a></li>
                        <li><a href="book.html">Book Now</a></li>
                    </ul>
                </div>
                
                <div class="breezwp-footer-section">
                    <h4>Amenities</h4>
                    <ul>
                        <li><a href="bedrooms.html">Bedrooms</a></li>
                        <li><a href="pool.html">Pool</a></li>
                        <li><a href="gameroom.html">Game Room</a></li>
                        <li><a href="resort-amenities.html">Resort</a></li>
                    </ul>
                </div>
                
                <div class="breezwp-footer-section">
                    <h4>Contact</h4>
                    <p><i class="fas fa-map-marker-alt"></i> Solterra Resort, Orlando, FL</p>
                    <p><i class="fas fa-phone"></i> <a href="tel:+14078018850">******-801-8850</a> (Call, Text / WhatsApp)</p>
                    <p><i class="fas fa-envelope"></i> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                </div>
            </div>
            
            <div class="breezwp-footer-bottom">
                <p>&copy; 2025 Breez Villa. All rights reserved. | Luxury Redefined.</p>
                <div class="breezwp-footer-links">
                    <a href="privacy.html">Privacy Policy</a>
                    <a href="terms.html">Terms of Service</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript for Navigation -->
    <script>
        // Mobile Navigation Toggle
        const navToggle = document.getElementById('nav-toggle');
        const navMenu = document.getElementById('nav-menu');
        
        navToggle.addEventListener('click', () => {
            navMenu.classList.toggle('active');
            navToggle.classList.toggle('active');
        });
        
        // Close mobile menu when clicking on a link
        document.querySelectorAll('.breezwp-nav-link').forEach(link => {
            link.addEventListener('click', () => {
                navMenu.classList.remove('active');
                navToggle.classList.remove('active');
            });
        });
        
        // Dropdown functionality
        document.querySelectorAll('.breezwp-dropdown').forEach(dropdown => {
            dropdown.addEventListener('mouseenter', () => {
                dropdown.querySelector('.breezwp-dropdown-menu').style.display = 'block';
            });
            
            dropdown.addEventListener('mouseleave', () => {
                dropdown.querySelector('.breezwp-dropdown-menu').style.display = 'none';
            });
        });
        
        // OwnerRez Widget Height Enforcement
        function enforceOwnerRezHeight() {
            const widgets = document.querySelectorAll('.ownerrez-widget');
            widgets.forEach(widget => {
                // Force widget height
                widget.style.height = '400px';
                widget.style.minHeight = '400px';
                widget.style.maxHeight = '400px';
                widget.style.overflow = 'hidden';
                
                // Force iframe heights
                const iframes = widget.querySelectorAll('iframe');
                iframes.forEach(iframe => {
                    iframe.style.height = '400px';
                    iframe.style.minHeight = '400px';
                    iframe.style.maxHeight = '400px';
                    iframe.style.overflow = 'hidden';
                });
                
                // Force any other elements
                const allElements = widget.querySelectorAll('*');
                allElements.forEach(element => {
                    if (element.style.height || element.style.minHeight) {
                        element.style.height = '400px';
                        element.style.maxHeight = '400px';
                        element.style.overflow = 'hidden';
                    }
                });
            });
        }
        
        // Run height enforcement after page load and periodically
        document.addEventListener('DOMContentLoaded', enforceOwnerRezHeight);
        window.addEventListener('load', enforceOwnerRezHeight);
        
        // Check periodically for dynamically loaded content
        setInterval(enforceOwnerRezHeight, 2000);
        
        // Also run after OwnerRez script loads
        setTimeout(enforceOwnerRezHeight, 3000);
        setTimeout(enforceOwnerRezHeight, 5000);
    </script>
</body>
</html>
