# Breez Villa Website

A complete, responsive website for Breez Villa family vacation rental at Solterra Resort, Orlando.

## Features

- **Responsive Design**: Mobile-first approach with modern CSS Grid and Flexbox
- **Navigation**: Dropdown navigation with mobile hamburger menu
- **Photo Gallery**: Filterable gallery with hover effects
- **OwnerRez Integration**: Availability calendar, reviews, and booking widgets
- **Analytics Ready**: Placeholders for Google Analytics, Facebook Pixel, Microsoft Clarity, and chatbot
- **Modern UI**: Elegant design with smooth animations and hover effects

## Pages

1. **Home (index.html)** - Main landing page with property overview
2. **Availability (availability.html)** - Check availability with OwnerRez widget
3. **Photos (photos.html)** - Comprehensive photo gallery with filtering
4. **Reviews (reviews.html)** - Guest reviews and ratings
5. **Book (book.html)** - Booking page with OwnerRez widget and currency converter
6. **Amenities Pages** - Individual pages for different amenity categories
7. **Location, FAQ, Blog** - Additional informational pages

## Navigation Structure

- Home
- Availability
  - Check Availability
  - Calendar View
- Photos
- Reviews
- Amenities
  - Bedrooms
  - Pool
  - Gameroom
  - Kitchen & Dining
  - Resort Amenities
- Location
- FAQ
- Blog
- Book

## OwnerRez Widgets

The website includes three OwnerRez widgets:

1. **Availability Calendar** - Multiple month calendar view
2. **Reviews Widget** - Customer reviews display
3. **Booking Widget** - Direct booking and inquiry system

## Analytics Integration

Placeholders are included for:
- Google Analytics
- Facebook Pixel
- Microsoft Clarity
- Chatbot integration

## File Structure

```
breezvillas.com/
├── index.html              # Home page
├── availability.html       # Availability page
├── photos.html            # Photo gallery
├── reviews.html           # Reviews page
├── book.html              # Booking page
├── breezvillas.css        # Main stylesheet
├── images/                # Image assets
├── header.html            # Header component
├── footer.html            # Footer component
└── README.md              # This file
```

## Setup Instructions

1. **Upload Files**: Upload all HTML, CSS, and image files to your web server
2. **Update Analytics**: Replace placeholder comments with actual analytics codes
3. **Customize Content**: Update contact information, rates, and property details
4. **Test Functionality**: Verify all OwnerRez widgets are working correctly
5. **Mobile Testing**: Test responsive design on various devices

## Customization

### Colors
The website uses a custom color scheme defined in CSS variables:
- Primary: #9029a7 (Purple)
- Secondary: #602d92 (Dark Purple)
- Accent: #3bc3d4 (Teal)

### Fonts
- Primary: Open Sans (Google Fonts)
- Icons: Font Awesome 6.4.0

### Images
All images are stored in the `images/` folder and referenced with relative paths.

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers (iOS Safari, Chrome Mobile)

## Performance Features

- Optimized images with proper alt tags
- CSS animations with hardware acceleration
- Responsive images and layouts
- Minimal JavaScript for enhanced performance

## Contact Information

For website support or questions about Breez Villa:
- Email: <EMAIL>
- Phone: ******-801-8850 (Call, Text / WhatsApp)
- Location: Solterra Resort, Orlando, FL

## License

© 2025 Breez Villa. All rights reserved. Luxury Redefined.
