<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Book Your Stay - Breez Villa</title>
    <link rel="stylesheet" href="breezvillas.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Google Analytics Code Placeholder -->
    <!-- Replace with your actual Google Analytics code -->
    <!-- <script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script> -->
    
    <!-- Facebook Pixel Code Placeholder -->
    <!-- Replace with your actual Facebook Pixel code -->
    <!-- <script>!function(f,b,e,v,n,t,s)...</script> -->
    
    <!-- Microsoft Clarity Analytics Code Placeholder -->
    <!-- Replace with your actual Microsoft Clarity code -->
    <!-- <script type="text/javascript">(function(c,l,a,r,i,t,y){...})</script> -->
    
    <!-- Chatbot Code Placeholder -->
    <!-- Replace with your actual chatbot code -->
</head>
<body>
    <!-- Header Navigation -->
    <header class="breezwp-header">
        <nav class="breezwp-nav">
            <div class="breezwp-nav-container">
                <div class="breezwp-logo">
                    <a href="index.html">
                        <i class="fas fa-home"></i>
                        <span>Breez Villa</span>
                    </a>
                </div>
                
                <div class="breezwp-nav-menu" id="nav-menu">
                    <ul class="breezwp-nav-list">
                        <li><a href="index.html" class="breezwp-nav-link">Home</a></li>
                        <li class="breezwp-dropdown">
                            <a href="#" class="breezwp-nav-link">Availability <i class="fas fa-chevron-down"></i></a>
                            <ul class="breezwp-dropdown-menu">
                                <li><a href="availability.html">Check Availability</a></li>
                                <li><a href="calendar.html">Calendar View</a></li>
                            </ul>
                        </li>
                        <li><a href="photos.html" class="breezwp-nav-link">Photos</a></li>
                        <li><a href="reviews.html" class="breezwp-nav-link">Reviews</a></li>
                        <li class="breezwp-dropdown">
                            <a href="#" class="breezwp-nav-link">Amenities <i class="fas fa-chevron-down"></i></a>
                            <ul class="breezwp-dropdown-menu">
                                <li><a href="bedrooms.html">Bedrooms</a></li>
                                <li><a href="pool.html">Pool</a></li>
                                <li><a href="gameroom.html">Gameroom</a></li>
                                <li><a href="kitchen-dining.html">Kitchen & Dining</a></li>
                                <li><a href="resort-amenities.html">Resort Amenities</a></li>
                            </ul>
                        </li>
                        <li><a href="location.html" class="breezwp-nav-link">Location</a></li>
                        <li><a href="faq.html" class="breezwp-nav-link">FAQ</a></li>
                        <li><a href="blog.html" class="breezwp-nav-link">Blog</a></li>
                        <li><a href="book.html" class="breezwp-nav-link breezwp-book-now">Book Now</a></li>
                    </ul>
                </div>
                
                <div class="breezwp-nav-toggle" id="nav-toggle">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Page Header -->
    <section class="breezwp-page-header">
        <div class="breezwp-page-header-content">
            <h1>Book Your Stay</h1>
            <p>Secure your luxury vacation at Breez Villa</p>
        </div>
    </section>

    <!-- Main Content -->
    <main class="breezwp-main-content">
        <!-- Booking Widget -->
        <section class="breezwp-section breezwp-section-featured" id="booking-widget">
            <div class="breezwp-section-header">
                <div class="breezwp-icon-wrapper">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <h2>Reserve Your Dates</h2>
                <p>Book directly through our secure booking system with real-time availability</p>
            </div>

            <div class="breezwp-booking-container">
                <div class="breezwp-booking-widget">
                    <div class="breezwp-booking-form-header">
                        <h3><i class="fas fa-calendar-alt"></i> Booking Form</h3>
                        <p>Select your dates and complete your reservation</p>
                    </div>
                    <div class="ownerrez-container">
                        <!-- OwnerRez Booking/Inquiry widget for Breez Villa at Solterra Resort -->
                        <div class="ownerrez-widget" data-propertyId="9ce7dc1ccb45420199427773bff33387" data-widget-type="Wordpress_booking - Booking/Inquiry" data-widgetId="fd7e2adfe252468593db78f1eaa500c2"></div>
                        <script src="https://app.ownerrez.com/widget.js"></script>
                    </div>
                </div>

                <div class="breezwp-booking-sidebar">
                    <div class="breezwp-currency-converter">
                        <div class="breezwp-sidebar-header">
                            <h3><i class="fas fa-exchange-alt"></i> Currency Converter</h3>
                            <p>Check rates in your local currency</p>
                        </div>
                        <div class="breezwp-converter-widget">
                            <iframe
                                title="Currency Converter"
                                src="https://wise.com/gb/currency-converter/fx-widget/converter?sourceCurrency=USD&targetCurrency=GBP"
                                height="490"
                                width="100%"
                                style="border: none;"
                            ></iframe>
                        </div>
                    </div>

                    <div class="breezwp-optional-fees">
                        <div class="breezwp-sidebar-header">
                            <h3><i class="fas fa-plus-circle"></i> Optional Add-Ons</h3>
                            <p>Enhance your stay with these optional services</p>
                        </div>
                        <div class="breezwp-fees-list">
                            <div class="breezwp-fee-item">
                                <div class="breezwp-fee-icon">
                                    <i class="fas fa-thermometer-half"></i>
                                </div>
                                <div class="breezwp-fee-details">
                                    <h4>Pool Heating</h4>
                                    <p>Keep your pool at the perfect temperature year-round</p>
                                    <div class="breezwp-fee-price">$25 <span>per night</span></div>
                                    <small>Paid for entire stay duration</small>
                                </div>
                            </div>

                            <div class="breezwp-fee-item">
                                <div class="breezwp-fee-icon">
                                    <i class="fas fa-broom"></i>
                                </div>
                                <div class="breezwp-fee-details">
                                    <h4>Mid-Stay Cleaning</h4>
                                    <p>Professional cleaning service during your stay</p>
                                    <div class="breezwp-fee-price">$250 <span>per clean</span></div>
                                    <small>Perfect for extended stays</small>
                                </div>
                            </div>
                        </div>
                        <div class="breezwp-fees-note">
                            <i class="fas fa-info-circle"></i>
                            <p>Optional services can be added during booking confirmation or by contacting our team.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Booking Information -->
        <section class="breezwp-section">
            <div class="breezwp-section-header">
                <div class="breezwp-icon-wrapper">
                    <i class="fas fa-info-circle"></i>
                </div>
                <h2>Booking Information</h2>
                <p>Everything you need to know about reserving your stay</p>
            </div>

            <div class="breezwp-info-grid">
                <div class="breezwp-info-item">
                    <i class="fas fa-clock"></i>
                    <h3>Check-in & Check-out Times</h3>
                    <p><strong>Check-in:</strong> 4:00 PM</p>
                    <p><strong>Check-out:</strong> 10:00 AM</p>
                    <p>Early check-in and late check-out may be available upon request</p>
                </div>

                <div class="breezwp-info-item">
                    <i class="fas fa-calendar-alt"></i>
                    <h3>Minimum Stay Requirements</h3>
                    <p><strong>Standard:</strong> 3-night minimum stay</p>
                    <p><strong>Peak Season:</strong> 5-night minimum stay</p>
                    <p><strong>Holidays:</strong> 7-night minimum stay</p>
                </div>

                <div class="breezwp-info-item">
                    <i class="fas fa-credit-card"></i>
                    <h3>Payment Terms</h3>
                    <p><strong>Deposit:</strong> 20% required to confirm booking</p>
                    <p><strong>Final Payment:</strong> Due 60 days before arrival</p>
                    <p><strong>Accepted:</strong> All major credit cards, bank transfers</p>
                    <p>Secure payment processing with SSL encryption</p>
                </div>

                <div class="breezwp-info-item">
                    <i class="fas fa-shield-alt"></i>
                    <h3>Cancellation Policy</h3>
                    <p><strong>90+ days:</strong> 100% refund (less $50 processing fee)</p>
                    <p><strong>60-90 days:</strong> 50% refund (less $50 processing fee)</p>
                    <p><strong>Within 60 days:</strong> No refund</p>
                    <p><strong>Grace Period:</strong> Free cancellation within 24 hours of booking if arrival is 72+ hours away</p>
                    <p><em>Travel insurance is highly recommended</em></p>
                </div>
            </div>
        </section>

        <!-- Call to Action -->
        <section class="breezwp-cta-section">
            <div class="breezwp-cta-content">
                <h2>Ready to Start Your Adventure?</h2>
                <p>Book your stay at Breez Villa today and experience luxury family living in the heart of Orlando's premier resort community.</p>
                <div class="breezwp-cta-buttons">
                    <a href="#booking-widget" class="breezwp-book-btn">Book Now</a>
                    <a href="availability.html" class="breezwp-secondary-btn">Check Availability</a>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="breezwp-footer">
        <div class="breezwp-footer-content">
            <div class="breezwp-footer-grid">
                <div class="breezwp-footer-section">
                    <h3>Breez Villa</h3>
                    <p>Family vacation rental at Solterra Resort, Orlando</p>
                    <div class="breezwp-social-links">
                        <a href="#" aria-label="Facebook"><i class="fab fa-facebook"></i></a>
                        <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                        <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                    </div>
                </div>
                
                <div class="breezwp-footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="availability.html">Availability</a></li>
                        <li><a href="photos.html">Photos</a></li>
                        <li><a href="book.html">Book Now</a></li>
                    </ul>
                </div>
                
                <div class="breezwp-footer-section">
                    <h4>Amenities</h4>
                    <ul>
                        <li><a href="bedrooms.html">Bedrooms</a></li>
                        <li><a href="pool.html">Pool</a></li>
                        <li><a href="gameroom.html">Game Room</a></li>
                        <li><a href="resort-amenities.html">Resort</a></li>
                    </ul>
                </div>
                
                <div class="breezwp-footer-section">
                    <h4>Contact</h4>
                    <p><i class="fas fa-map-marker-alt"></i> Solterra Resort, Orlando, FL</p>
                    <p><i class="fas fa-phone"></i> <a href="tel:+14078018850">******-801-8850</a> (Call, Text / WhatsApp)</p>
                    <p><i class="fas fa-envelope"></i> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                </div>
            </div>
            
            <div class="breezwp-footer-bottom">
                <p>&copy; 2025 Breez Villa. All rights reserved. | Luxury Redefined.</p>
                <div class="breezwp-footer-links">
                    <a href="privacy.html">Privacy Policy</a>
                    <a href="terms.html">Terms of Service</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript for Navigation -->
    <script>
        // Mobile Navigation Toggle
        const navToggle = document.getElementById('nav-toggle');
        const navMenu = document.getElementById('nav-menu');
        
        navToggle.addEventListener('click', () => {
            navMenu.classList.toggle('active');
            navToggle.classList.toggle('active');
        });
        
        // Close mobile menu when clicking on a link
        document.querySelectorAll('.breezwp-nav-link').forEach(link => {
            link.addEventListener('click', () => {
                navMenu.classList.remove('active');
                navToggle.classList.remove('active');
            });
        });
        
        // Dropdown functionality
        document.querySelectorAll('.breezwp-dropdown').forEach(dropdown => {
            dropdown.addEventListener('mouseenter', () => {
                dropdown.querySelector('.breezwp-dropdown-menu').style.display = 'block';
            });
            
            dropdown.addEventListener('mouseleave', () => {
                dropdown.querySelector('.breezwp-dropdown-menu').style.display = 'none';
            });
        });
        
        // Dynamic OwnerRez Widget Height Management
        function manageDynamicOwnerRezHeight() {
            const bookingWidgets = document.querySelectorAll('.breezwp-booking-widget .ownerrez-widget');
            const otherWidgets = document.querySelectorAll('.breezwp-availability-widget .ownerrez-widget, .breezwp-reviews-widget .ownerrez-widget');

            // Handle booking widgets with dynamic sizing
            bookingWidgets.forEach(widget => {
                widget.style.height = 'auto';
                widget.style.minHeight = '400px';
                widget.style.maxHeight = 'none';
                widget.style.overflow = 'visible';

                // Allow iframes in booking widgets to expand
                const iframes = widget.querySelectorAll('iframe');
                iframes.forEach(iframe => {
                    iframe.style.height = 'auto';
                    iframe.style.minHeight = '400px';
                    iframe.style.maxHeight = 'none';
                    iframe.style.overflow = 'visible';
                });
            });

            // Handle other widgets with fixed sizing
            otherWidgets.forEach(widget => {
                widget.style.height = '600px';
                widget.style.minHeight = '600px';
                widget.style.maxHeight = '600px';
                widget.style.overflow = 'hidden';

                const iframes = widget.querySelectorAll('iframe');
                iframes.forEach(iframe => {
                    iframe.style.height = '600px';
                    iframe.style.minHeight = '600px';
                    iframe.style.maxHeight = '600px';
                    iframe.style.overflow = 'hidden';
                });
            });
        }

        // Observe OwnerRez widget content changes
        function observeOwnerRezChanges() {
            const bookingWidgets = document.querySelectorAll('.breezwp-booking-widget .ownerrez-widget');

            bookingWidgets.forEach(widget => {
                const observer = new MutationObserver((mutations) => {
                    mutations.forEach((mutation) => {
                        if (mutation.type === 'childList' || mutation.type === 'attributes') {
                            // Re-apply dynamic sizing when content changes
                            setTimeout(manageDynamicOwnerRezHeight, 100);
                        }
                    });
                });

                observer.observe(widget, {
                    childList: true,
                    subtree: true,
                    attributes: true,
                    attributeFilter: ['style', 'height']
                });
            });
        }

        // Initialize dynamic height management
        document.addEventListener('DOMContentLoaded', () => {
            manageDynamicOwnerRezHeight();
            observeOwnerRezChanges();
        });

        window.addEventListener('load', () => {
            manageDynamicOwnerRezHeight();
            observeOwnerRezChanges();
        });

        // Periodic checks for dynamically loaded content
        setInterval(manageDynamicOwnerRezHeight, 3000);

        // Run after OwnerRez script loads
        setTimeout(() => {
            manageDynamicOwnerRezHeight();
            observeOwnerRezChanges();
        }, 2000);

        setTimeout(() => {
            manageDynamicOwnerRezHeight();
            observeOwnerRezChanges();
        }, 5000);
    </script>
</body>
</html>